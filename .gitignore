Dockerrun.aws.json
dist/
.env

###############################

# Cruft
*~
*#
.#*
.DS_Store


# Test
coverage
.eslintcache

# Logs
*.log

# npm
node_modules/
.npm
npm-debug.log*
/build/bundle.js
/build/index.html
/build*
/build/*

# Yarn
**/.pnp.*
**/.yarn/*
!**/.yarn/patches
!**/.yarn/plugins
!**/.yarn/releases
!**/.yarn/sdks
!**/.yarn/versions

# Editor/IDE
.idea
packages/api/etc/

############## blackbox
/.blackbox/pubring.gpg~
/.blackbox/pubring.kbx~
/.blackbox/secring.gpg
/packages/server/.env
/packages/server/.env.production
/packages/dev/.env

# Elastic Beanstalk Files
.elasticbeanstalk/*
!.elasticbeanstalk/*.cfg.yml
!.elasticbeanstalk/*.global.yml
*.sql
!packages/dev/test-db/**/*.sql

/packages/server/.env.uat
/.env.ci
