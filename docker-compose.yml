version: "2"

x-envDefaults: &envDefaults
  APOLLO_ENGINE_API_KEY: ""
  DATABASE_READER_URL: "mysql://root:wisdom@mysql/wisdom_test"
  DATABASE_URL: "mysql://root:wisdom@mysql/wisdom_test"
  DATABASE_WRITER_URL: "mysql://root:wisdom@mysql/wisdom_test"
  DEBUG: ""
  GOOGLE_CLIENT_EMAIL: ""
  GOOGLE_CLOUD_FILES_BUCKET: ""
  GOOGLE_CLOUD_PROJECT_ID: ""
  GOOGLE_PRIVATE_KEY: ""
  LOG_LEVEL: "debug"
  MAILCHIMP_API_KEY: ""
  NODE_ENV: "test"
  PORT: "8080"
  RAVEN_DSN: ""
  REDIS_URL: "redis://redis"
  SITE_URL: "http://wisdom-client:3000"
  SMTP_HOST: ""
  SMTP_PASSWORD: ""
  SMTP_USER: "apikey"
  STAGE: "test"
  STRIPE_SK: "sk_test_UVv5KJO3xtwTPDlha3pDFmcf"
  SUMO_LOGIC_COLLECTION_URL: ""
  ZENDESK_TOKEN: ""
  ZENDESK_URL: ""
  ZENDESK_USERNAME: ""

x-generalBuildDefaults: &generalBuildDefaults
  context: .
  dockerfile: Dockerfile.ci.general
  args:
    - GITHUB_TOKEN
    - FONT_AWESOME_TOKEN
    - NODE_ENV=development

services:
  mysql:
    image: arm64v8/mysql:8.0.32
    environment:
      MYSQL_ROOT_PASSWORD: wisdom
      MYSQL_DATABASE: wisdom_test
    healthcheck:
      test: "mysqladmin ping -h 127.0.0.1 -u root -p$$MYSQL_ROOT_PASSWORD"
      interval: 3s
      timeout: 3s
      retries: 20

  redis:
    image: arm64v8/redis:6.2.10-alpine
    healthcheck:
      test: "[ $$(redis-cli ping) = 'PONG' ]"
      interval: 3s
      timeout: 3s
      retries: 20

  server:
    build:
      context: packages/server
      dockerfile: Dockerfile.ci.server
      args:
        - GITHUB_TOKEN
        - FONT_AWESOME_TOKEN
    working_dir: /app
    environment:
      - GITHUB_TOKEN
      - FONT_AWESOME_TOKEN

  web:
    build:
      context: packages/web
      dockerfile: Dockerfile.ci.web
      args:
        - GITHUB_TOKEN
        - FONT_AWESOME_TOKEN
    working_dir: /app
    environment:
      - GITHUB_TOKEN
      - FONT_AWESOME_TOKEN

  build:
    build:
      <<: *generalBuildDefaults
    working_dir: /app
    environment:
      - AWS_ACCESS_KEY_ID
      - AWS_DEFAULT_REGION
      - AWS_REGION
      - AWS_SECRET_ACCESS_KEY
      - BUILDKITE_ANALYTICS_TOKEN
      - BUILDKITE_BUILD_ID
      - BUILDKITE_BUILD_NUMBER
      - BUILDKITE_JOB_ID
      - BUILDKITE_BRANCH
      - BUILDKITE_COMMIT
      - BUILDKITE_MESSAGE
      - BUILDKITE_BUILD_URL
      - CI=true
      - GITHUB_TOKEN
      - FONT_AWESOME_TOKEN
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock

  prepare-database:
    build:
      <<: *generalBuildDefaults
    environment:
      <<: *envDefaults
    command: "bash /app/scripts/prepareDatabase.sh"
    depends_on:
      mysql:
        condition: service_healthy

  wisdom-server:
    image: "964996960114.dkr.ecr.us-west-2.amazonaws.com/wisdom-server:ci-${BUILDKITE_COMMIT}"
    environment:
      <<: *envDefaults
    depends_on:
      prepare-database:
        condition: service_completed_successfully
      redis:
        condition: service_healthy
    healthcheck:
      test: "curl --fail http://localhost:$$PORT/status"
      interval: 3s
      timeout: 3s
      retries: 20

  wisdom-client:
    image: "964996960114.dkr.ecr.us-west-2.amazonaws.com/wisdom-server:ci-client-${BUILDKITE_COMMIT}"
    environment:
      <<: *envDefaults
      PORT: "3000"
    depends_on:
      wisdom-server:
        condition: service_healthy
    healthcheck:
      test: "curl --fail http://localhost:$$PORT/"
      interval: 3s
      timeout: 3s
      retries: 20

  integration:
    build:
      <<: *generalBuildDefaults
    depends_on:
      wisdom-server:
        condition: service_healthy
      wisdom-client:
        condition: service_healthy
    working_dir: /app
    environment:
      - CI=1
      - NODE_ENV=test
      - BASE_URL=http://wisdom-client:3000
      - SERVER_URL=http://wisdom-server:8080
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "./tmp/app/packages/web/playwright-report:/app/packages/web/playwright-report"
      - "./tmp/app/packages/web/test-results:/app/packages/web/test-results"
