steps:
  - name: ":typescript: server tests"
    command: "yarn run test:ci"
    plugins:
      - docker-compose#v5.6.0:
          environment:
            - BUILDKITE_ANALYTICS_TOKEN=iveqFsTZpQByvcoPSPQ2UWTV
          propagate-environment: true
          run: server
          upload-container-logs: always
    retry:
      automatic:
        - exit_status: 129
          limit: 10
        - exit_status: "*"
          limit: 1

  - name: ":typescript: web tests"
    command: "yarn run test:ci"
    plugins:
      - docker-compose#v5.6.0:
          environment:
            - BUILDKITE_ANALYTICS_TOKEN=wFqvoYPuJUUCNNa2wqA4eCPH
          propagate-environment: true
          run: web
          upload-container-logs: always

  - name: ":docker: build client"
    key: "build-client"
    command: "yarn run build:client"
    plugins:
      - docker-compose#v5.6.0:
          propagate-environment: true
          run: build
          upload-container-logs: always

  - name: ":docker: build server"
    key: "build-server"
    command: "yarn run build:server"
    plugins:
      - docker-compose#v5.6.0:
          propagate-environment: true
          run: build
          upload-container-logs: always

  - group: ":playwright: integration"
    depends_on:
      - "build-server"
      - "build-client"
    steps:
      - name: "ECR authentication"
        key: "ecr-auth"
        command: "aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 964996960114.dkr.ecr.us-west-2.amazonaws.com"
        retry:
          manual:
            permit_on_passed: true

      - name: ":playwright: integration tests"
        depends_on:
          - "ecr-auth"
        command: "bash /app/scripts/runIntegration.sh"
        retry:
          manual:
            permit_on_passed: true
        plugins:
          - docker-compose#v5.6.0:
              environment:
                - BUILDKITE_ANALYTICS_TOKEN=KwppJFK9q8kRfMHBCAF6ELS3
              propagate-environment: true
              run: integration
              upload-container-logs: always
        artifact_paths:
          - "./tmp/app/packages/web/playwright-report/**"
          - "./tmp/app/packages/web/playwright-report/**/*.png"
          - "./tmp/app/packages/web/test-results/**"
          - "./tmp/app/packages/web/test-results/**/*.png"
          - "./tmp/app/packages/web/test-results/**/*.webm"
