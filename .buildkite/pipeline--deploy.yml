steps:
  - name: ":docker: build uat"
    key: "build-uat"
    command: "yarn run build:uat"
    plugins:
      - docker-compose#v5.6.0:
          run: build
  - name: ":docker: build production"
    key: "build-production"
    command: "yarn run build:production"
    plugins:
      - docker-compose#v5.6.0:
          run: build
  - name: "deploy uat server"
    key: "deploy-uat--server"
    depends_on:
      - "build-uat"
    command: "yarn run deploy:uat:server"
    plugins:
      - docker-compose#v5.6.0:
          run: build
  - name: "deploy uat workers lime"
    key: "deploy-uat--workers--lime"
    depends_on:
      - "build-uat"
    command: "yarn run deploy:uat:workers:lime"
    plugins:
      - docker-compose#v5.6.0:
          run: build
  - name: ":cypress: uat smoke tests"
    depends_on:
      - "deploy-uat--server"
    command: "TARGET=uat bash ./scripts/runSmoke.sh"
  - block: ":rocket: release production"
    key: "release-production"
    depends_on:
      - "build-production"
  - name: "deploy production server"
    key: "deploy-production--server"
    depends_on:
      - "release-production"
    command: "yarn run deploy:production:server"
    plugins:
      - docker-compose#v5.6.0:
          run: build
  - name: "deploy production workers coconut"
    key: "deploy-production--workers--coconut"
    depends_on:
      - "release-production"
    command: "yarn run deploy:production:workers:coconut"
    plugins:
      - docker-compose#v5.6.0:
          run: build
  - name: "deploy production workers mint"
    key: "deploy-production--workers--mint"
    depends_on:
      - "release-production"
    command: "yarn run deploy:production:workers:mint"
    plugins:
      - docker-compose#v5.6.0:
          run: build
  - name: ":cypress: production smoke tests"
    depends_on:
      - "deploy-production--server"
    command: "TARGET=production bash ./scripts/runSmoke.sh"
