FROM node:20.18-alpine AS base
RUN apk --no-cache add curl

######################################

FROM base AS deps
ARG GITHUB_TOKEN
ARG FONT_AWESOME_TOKEN

RUN corepack enable && corepack prepare yarn@4.5.1 --activate

WORKDIR /app
COPY ./packages/web/.npmrc ./packages/web/package.json ./packages/web/.yarnrc.yml ./packages/web/yarn.lock ./
RUN yarn install --immutable
RUN rm -rf .npmrc .yarnrc.yml

######################################

FROM deps AS build-client
ARG VERSION

WORKDIR /app
COPY ./packages/web .

ENV NODE_ENV test
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"
ENV NEXT_TELEMETRY_DISABLED 1
ENV NEXT_PUBLIC_VERSION=$VERSION

RUN npm run next-build

######################################

FROM build-client AS runner

WORKDIR /app
COPY --from=build-client /app/public ./public
COPY --from=build-client /app/.next/standalone ./
COPY --from=build-client /app/.next/static ./.next/static

USER root

EXPOSE 3000

# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/next-config-js/output
CMD ["node", "server.js"]
