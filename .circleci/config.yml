version: 2.1

orbs:
  docker: circleci/docker@2.2.0
  eb: circleci/aws-elastic-beanstalk@2.0.1

jobs:
  prepare-dependencies:
    resource_class: small
    docker:
      - image: cimg/node:18.16
    steps:
      - checkout
      - run:
          name: Compute version number
          command: echo "0.0.${CIRCLE_BUILD_NUM}-${CIRCLE_SHA1:0:7}" | tee version.txt
      - run:
          name: Install yarn
          command: yarn set version 3.2.0
      - run:
          name: Install yarn packages
          command: yarn install
      - persist_to_workspace:
          root: .
          paths:
            - .

  build-uat:
    resource_class: arm.medium
    machine:
      image: ubuntu-2004:202111-01
      docker_layer_caching: true
    steps:
      - attach_workspace:
          at: .
      - run:
          name: sign in to aws ecr
          command: |
            aws ecr get-login-password | docker login --username AWS --password-stdin 964996960114.dkr.ecr.us-west-2.amazonaws.com
      - run:
          name: build
          command: |
            export __BUILD_VERSION="$(cat version.txt)"
            yarn run build:uat
      - persist_to_workspace:
          root: .
          paths:
            - .

  deploy-uat--servers:
    resource_class: small
    docker:
      - image: cimg/base:stable
    steps:
      - attach_workspace:
          at: .
      - eb/setup
      - run:
          name: deploy servers
          command: |
            bash ./scripts/releaseUat.sh

  deploy-uat--workers:
    resource_class: small
    docker:
      - image: cimg/base:stable
    steps:
      - attach_workspace:
          at: .
      - eb/setup
      - run:
          name: deploy workers
          command: |
            bash ./scripts/releaseUatWorkers.sh

  build-production:
    resource_class: arm.medium
    machine:
      image: ubuntu-2004:202111-01
      docker_layer_caching: true
    steps:
      - attach_workspace:
          at: .
      - run:
          name: sign in to aws ecr
          command: |
            aws ecr get-login-password | docker login --username AWS --password-stdin 964996960114.dkr.ecr.us-west-2.amazonaws.com
      - run:
          name: build
          command: |
            export __BUILD_VERSION="$(cat version.txt)"
            yarn run build:production
      - persist_to_workspace:
          root: .
          paths:
            - .

  deploy-production--servers:
    resource_class: small
    docker:
      - image: cimg/base:stable
    steps:
      - attach_workspace:
          at: .
      - eb/setup
      - run:
          name: deploy servers
          command: |
            bash ./scripts/releaseProduction.sh

  deploy-production--workers:
    resource_class: small
    docker:
      - image: cimg/base:stable
    steps:
      - attach_workspace:
          at: .
      - eb/setup
      - run:
          name: deploy workers
          command: |
            bash ./scripts/releaseProductionWorkers.sh

  test-server:
    resource_class: large
    machine:
      image: ubuntu-2004:202111-01
    steps:
      - attach_workspace:
          at: .

      # server packages
      - restore_cache:
          keys:
            - server-yarn-deps-{{ arch }}-{{ checksum "packages/server/yarn.lock" }}
            - server-yarn-deps-{{ arch }}
            - server-yarn-deps
      - run:
          name: yarn install server
          command: yarn install --immutable
          working_directory: packages/server
      - save_cache:
          paths:
            - packages/server/node_modules
          key: server-yarn-deps-{{ arch }}-{{ checksum "packages/server/yarn.lock" }}-{{ epoch }}

      - run:
          name: test
          working_directory: packages/server
          no_output_timeout: 5m
          command: |
            yarn run test:ci

  test-web:
    resource_class: small
    docker:
      - image: cimg/node:18.16
    steps:
      - attach_workspace:
          at: .

      # web packages
      - restore_cache:
          keys:
            - web-yarn-deps-{{ arch }}-{{ checksum "packages/web/yarn.lock" }}
            - web-yarn-deps-{{ arch }}
            - web-yarn-deps
      - run:
          name: yarn install web
          command: yarn install --immutable
          working_directory: packages/web
      - save_cache:
          paths:
            - packages/web/node_modules
          key: web-yarn-deps-{{ arch }}-{{ checksum "packages/web/yarn.lock" }}-{{ epoch }}

      - run:
          name: test
          working_directory: packages/web
          command: |
            yarn run test:ci

  test-integration:
    resource_class: large
    machine:
      image: ubuntu-2004:202111-01

    steps:
      - attach_workspace:
          at: .

      - run:
          name: Start docker mysql
          command: docker run -d -p 3306:3306 -e MYSQL_ROOT_PASSWORD=wisdom -e MYSQL_DATABASE=wisdom_test mysql:8.0 mysqld  --innodb-buffer-pool-size=128M --performance_schema=0 --default-authentication-plugin=mysql_native_password

      - run:
          name: Start docker redis
          command: docker run -d -p 6379:6379 redis:6.2

      - run:
          name: Install apt packages
          command: |
            sudo apt-get -o Acquire::Check-Valid-Until=false --allow-releaseinfo-change update
            sudo apt-get install -y build-essential default-mysql-client libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libgconf-2-4 libnss3 libxss1 libasound2 libxtst6 xauth xvfb netcat

      - restore_cache:
          keys:
            - integration-yarn-deps-{{ arch }}-{{ checksum "yarn.lock" }}
            - integration-yarn-deps-{{ arch }}
            - integration-yarn-deps
      - run:
          name: yarn install root
          command: yarn install --immutable
      - save_cache:
          paths:
            - node_modules
          key: integration-yarn-deps-{{ arch }}-{{ checksum "yarn.lock" }}-{{ epoch }}

      # server packages
      - restore_cache:
          keys:
            - integration-server-yarn-deps-{{ arch }}-{{ checksum "packages/server/yarn.lock" }}
            - integration-server-yarn-deps-{{ arch }}
            - integration-server-yarn-deps
      - run:
          name: yarn install server
          command: yarn install --immutable
          working_directory: packages/server
      - save_cache:
          paths:
            - packages/server/node_modules
          key: integration-server-yarn-deps-{{ arch }}-{{ checksum "packages/server/yarn.lock" }}-{{ epoch }}

      # web packages
      - restore_cache:
          keys:
            - integration-web-yarn-deps-{{ arch }}-{{ checksum "packages/web/yarn.lock" }}
            - integration-web-yarn-deps-{{ arch }}
            - integration-web-yarn-deps
      - run:
          name: yarn install web
          command: yarn install --immutable
          working_directory: packages/web
      - save_cache:
          paths:
            - packages/web/node_modules
          key: integration-web-yarn-deps-{{ arch }}-{{ checksum "packages/web/yarn.lock" }}-{{ epoch }}

      - run:
          name: Waiting for MySQL to be ready
          command: |
            for i in `seq 1 10`;
            do
            nc -z 127.0.0.1 3306 && echo Success && exit 0
            echo -n .
            sleep 1
            done
            echo Failed waiting for MySQL && exit 1
      - run:
          name: Load test db
          command: |
            cd packages/dev
            mysql -u root --password=wisdom -h 127.0.0.1 -P 3306 wisdom_test < test-db.sql
      - run:
          name: Seed test db
          command: |
            cd packages/server
            BECOME_ENV=test npm run db:seed:test
      - run:
          name: Install cypress
          command: |
            cd packages/web
            DEBUG=cypress:* npx cypress install
            DEBUG=cypress:* npx cypress verify
      - run:
          name: Build and prepare frontend
          command: |
            cd packages/web
            yarn run build:test
            rm -rf ../../packages/server/src/build
            mv build ../../packages/server/src/
      - run:
          name: Start server and run tests
          command: |
            cd packages/server
            BECOME_ENV=test npm run start:test >server.log 2>&1 &
            npx wait-on http://localhost:8080/status
            cd ../../packages/web
            yarn run cypress:run:ci
      - store_artifacts:
          path: packages/web/cypress/screenshots
      - store_test_results:
          path: packages/web/cypress/results

workflows:
  version: 2
  # build-deploy:
  #     jobs:
  #         - prepare-dependencies:
  #               filters:
  #                   branches:
  #                       only: deploy
  #         - build-uat:
  #               requires:
  #                   - prepare-dependencies
  #         - deploy-uat--servers:
  #               requires:
  #                   - build-uat
  #         - deploy-uat--workers:
  #               requires:
  #                   - build-uat
  #         - build-production:
  #               requires:
  #                   - prepare-dependencies
  #         - hold-deploy-production:
  #               type: approval
  #               requires:
  #                   - build-production
  #         - deploy-production--servers:
  #               requires:
  #                   - hold-deploy-production
  #         - deploy-production--workers:
  #               requires:
  #                   - hold-deploy-production
  test:
    jobs:
      - prepare-dependencies:
          filters:
            branches:
              ignore:
                - deploy
                - buildkite
                - buildkite--deploy
                - f/buildkite
      - test-server:
          requires:
            - prepare-dependencies
      - test-web:
          requires:
            - prepare-dependencies
      - test-integration:
          requires:
            - prepare-dependencies
