services:
  - mysql:5.6

variables:
  CYPRESS_CACHE_FOLDER: "$CI_PROJECT_DIR/cache/Cypress"
  MYSQL_DATABASE: wisdom_test
  MYSQL_ROOT_PASSWORD: wisdom
  npm_config_cache: "$CI_PROJECT_DIR/.npm"

cache:
  paths:
    - .npm
    - cache/Cypress

stages:
  - test
  - integration

test-server:
  image: cypress/browsers:chrome69
  stage: test
  script:
    - cd packages/server
    - npm i npm@latest -g
    - npm config set //registry.npmjs.org/:_authToken ${NPM_TOKEN}
    - npm ci
    - npm run test:ci

test-web:
  image: cypress/browsers:chrome69
  stage: test
  script:
    - cd packages/web
    - npm i npm@latest -g
    - npm config set //registry.npmjs.org/:_authToken ${NPM_TOKEN}
    - npm ci
    - npm run test:ci

test-integration:
  image: cypress/browsers:node10.16.3-chrome80-ff73
  stage: integration
  script:
    - apt-get -o Acquire::Check-Valid-Until=false update
    - apt-get install -y default-mysql-client
    - cd packages/dev
    - mysql -u root --password=wisdom -h mysql wisdom_test < test-db.sql
    - cd ../../
    - npm i npm@latest -g
    - npm config set //registry.npmjs.org/:_authToken ${NPM_TOKEN}
    - npm ci
    - cd packages/web
    - npm ci
    - npm run build:test
    - mv build ../../packages/server
    - cd ../../packages/server
    - npm ci
    - BECOME_ENV=test npm run db:seed:test
    - BECOME_ENV=test npm run start:production > server.log 2>&1 &
    - npx wait-on http://localhost:8080/status
    - cd ../../packages/web
    - DEBUG=cypress:* npx cypress install
    - "sed -i.bak 's/smokeTestTimeout: 10000/smokeTestTimeout: 60000/' node_modules/cypress/lib/tasks/verify.js"
    - DEBUG=cypress:* npx cypress verify
    - npm run cypress:run:ci
    # - npm run cypress:run:record
  artifacts:
    expire_in: 1 week
    when: always
    paths:
      - packages/server/combined.log
      - packages/web/cypress/screenshots
      - packages/web/cypress/videos
