import path from "node:path";
import { fileURLToPath } from "node:url";

import { includeIgnoreFile } from "@eslint/compat";
import js from "@eslint/js";
import typescriptEslint from "@typescript-eslint/eslint-plugin";
import typescriptParser from "@typescript-eslint/parser";
import importPlugin from "eslint-plugin-import";
import noOnlyTests from "eslint-plugin-no-only-tests";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const gitignorePath = path.resolve(__dirname, ".gitignore");

export default [
	// Include .gitignore patterns
	includeIgnoreFile(gitignorePath),

	// TypeScript-specific configuration
	{
		files: ["**/*.{ts,tsx}"],
		rules: {
			// Apply TypeScript recommended rules for TS files
			...typescriptEslint.configs.recommended.rules,
		},
	},

	// Base configuration for all files
	{
		files: ["**/*.{js,jsx,ts,tsx}"],
		languageOptions: {
			ecmaVersion: "latest",
			sourceType: "module",
			parser: typescriptParser,
		},
		plugins: {
			"@typescript-eslint": typescriptEslint,
			import: importPlugin,
			"no-only-tests": noOnlyTests,
		},
		rules: {
			...js.configs.recommended.rules,
			...importPlugin.configs.recommended.rules,

			"arrow-body-style": "off",
			"arrow-parens": "off",
			camelcase: "warn",
			"class-methods-use-this": [
				"warn",
				{
					exceptMethods: ["render"],
				},
			],
			camelcase: "warn",
			"comma-dangle": ["warn", "only-multiline"],
			"consistent-return": "warn",
			eqeqeq: "warn",
			"function-paren-newline": "off",
			"implicit-arrow-linebreak": "off",
			"import/extensions": "off",
			"import/newline-after-import": "warn",
			"import/no-extraneous-dependencies": [
				"warn",
				{
					devDependencies: true,
				},
			],
			"import/no-unresolved": [
				"error",
				{
					ignore: ["$.*", "@.*"],
				},
			],
			"import/order": [
				"warn",
				{
					pathGroups: [
						{
							pattern: "lodash",
							group: "builtin",
							position: "before",
						},
						{
							pattern: "react",
							group: "builtin",
							position: "before",
						},
						{
							pattern: "achievable-me/**",
							group: "builtin",
							position: "after",
						},
						{
							pattern: "apollo/**",
							group: "builtin",
							position: "after",
						},
						{
							pattern: "$*",
							group: "internal",
							position: "after",
						},
						{
							pattern: "$*/**",
							group: "internal",
							position: "after",
						},
						{
							pattern: "@/**",
							group: "internal",
							position: "after",
						},
						{
							pattern: "./*.scss",
							group: "object",
							position: "after",
						},
						{
							pattern: "../*.scss",
							group: "object",
							position: "after",
						},
						{
							pattern: "./*",
							group: "sibling",
							position: "after",
						},
						{
							pattern: "../*",
							group: "parent",
							position: "after",
						},
					],
					groups: ["builtin", "external", "internal", "parent", "sibling", "index", "object"],
					pathGroupsExcludedImportTypes: ["lodash", "react"],
					"newlines-between": "always",
					alphabetize: {
						order: "asc",
						caseInsensitive: true,
					},
				},
			],
			"import/prefer-default-export": "off",
			indent: "off",
			"max-len": "off",
			"newline-per-chained-call": "off",
			"no-debugger": "warn",
			"no-multiple-empty-lines": "warn",
			"no-only-tests/no-only-tests": "error",
			"no-param-reassign": "warn",
			"no-restricted-syntax": "off",
			"no-shadow": "off",
			"no-tabs": "off",
			"no-underscore-dangle": "off",
			"no-unreachable": "warn",
			"no-unused-vars": "off",
			"object-curly-newline": "off",
			"operator-linebreak": "off",
			"prefer-destructuring": "warn",
			"quote-props": "off",
			quotes: [
				"warn",
				"single",
				{
					avoidEscape: true,
				},
			],
			semi: "warn",
			"space-infix-ops": "warn",

			// TypeScript rules
			"@typescript-eslint/no-shadow": "error",
			"@typescript-eslint/no-unused-vars": [
				"warn",
				{
					argsIgnorePattern: "^_",
					varsIgnorePattern: "^_",
					caughtErrorsIgnorePattern: "^_",
				},
			],
			"@typescript-eslint/no-explicit-any": "off",
			"@typescript-eslint/ban-ts-comment": "off",
		},
		settings: {
			"import/resolver": {
				node: {
					extensions: [".js", ".jsx", ".ts", ".tsx"],
				},
			},
		},
	},
];
