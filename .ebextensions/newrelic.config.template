files:
  "/etc/newrelic-infra.yml" :
    mode: "000644"
    owner: root
    group: root
    content: |
      license_key: 2d68b5fef1322aaa0060aaa77a530bdf2936NRAL
      custom_attributes:
        stage: STAGE
        appName: wisdom

commands:
# Create the agent’s yum repository
  "01-agent-repository":
    command: sudo curl -o /etc/yum.repos.d/newrelic-infra.repo https://download.newrelic.com/infrastructure_agent/linux/yum/amazonlinux/2/aarch64/newrelic-infra.repo

# Update your yum cache
  "02-update-yum-cache":
    command: sudo yum -q makecache -y --disablerepo='*' --enablerepo='newrelic-infra'

# Run the installation script
  "03-run-installation-script":
    command: sudo yum install newrelic-infra -y
