nodeLinker: node-modules

npmRegistries:
  "https://npm.fontawesome.com/":
    npmAlwaysAuth: true
    npmAuthToken: "${FONT_AWESOME_TOKEN}"
  "https://npm.pkg.github.com":
    npmAlwaysAuth: true
    npmAuthToken: "${GITHUB_TOKEN}"

npmScopes:
  achievable-me:
    npmRegistryServer: "https://npm.pkg.github.com/"
  fortawesome:
    npmRegistryServer: "https://npm.fontawesome.com/"

nmHoistingLimits: "workspaces"
