# Status

[![Build status](https://badge.buildkite.com/8201578e998cb73f4ecbce6d75c988379336cb0f43f0e90cee.svg)](https://buildkite.com/achievable/wisdom-ci)

# Overview

This is a Node.js-based repository that uses Apollo GraphQL, Next.js, and TypeScript for a learning platform.

# Setup

## Versions

- Node: `20.18.1` (as of 20250609)
- Yarn: `4.5.1` (as of 20250611)

## Required tokens

- Export from shell profile, e.g. in `~/.bashrc` add:
  - `export GITHUB_TOKEN={{ YOUR_GITHUB_TOKEN }}`
  - `export FONT_AWESOME_TOKEN={{ YOUR_FONT_AWESOME_TOKEN }}`

## Install dependencies

- External dependencies: `MySQL` and `Redis` (run via docker/docker-compose)
- `wisdom/` run `yarn install`
- `tundra-wizard/` run `yarn install`
- `server/` run `yarn install`
- `web/` run `yarn install`
- `dev/` run `yarn install`

**Note**: Install dependencies with `yarn install` in the root directory and in each package.

## Database setup

1. In `dev/`, create folder: `mysql/data-test`
2. Start database, from `dev/` run: `docker compose -p wisdom up mysql-test`
3. Create MySQL database `wisdom_dev`:
   - Access MySQL shell: `mysql -h 127.0.0.1 -u root -p`
   - Create database: `create database wisdom_dev;`
4. From `dev/`, run db setup script: `yarn run mysql:setup:local`
5. From `dev/`, run test-db import script: `DUMPNAME=test-db.sql yarn run mysql:refresh:localFromEnv`
6. From `server/`, run db seed script: `yarn run db:seed:test`
7. After db initialization, just run `docker compose -p wisdom up mysql-test` from `dev/` to start database
8. To restore test database to initial state (refresh database):
   - From `dev/`, run: `DUMPNAME=test-db.sql yarn run mysql:refresh:localFromEnv`
   - From `server/`, run: `yarn run db:seed:test`

## Database migrations

Database models are defined in the `daos/` folder and need a corresponding migrations file in `migrations/`.

Full process for database migrations:

1. Add/update `daos`
2. Add migrations
3. Shut down MySQL database
4. Delete the MySQL data folder (`packages/dev/mysql/data-test`)
5. Start MySQL database
6. Create `wisdom_dev` database
   - `mysql -h 127.0.0.1 -u root -p`
   - `create database wisdom_dev;`
7. Run the database setup script (in `dev/`: `yarn run mysql:setup:local`)
8. Pull in `test-db.sql` (from `dev/`: `DUMPNAME=test-db.sql yarn run mysql:refresh:localFromEnv`)
9. Run the migrate database script (from `server/`: `yarn run db:migrate:local`)
10. Run the regenerate database script (from `dev/`: `SOURCE=local bash ./scripts/generateTestDb.sh`)
11. If new tables have been added with data that needs to be persisted to `test-db.sql`, the table names need to be added to `./scripts/generateTestDb.sh`
12. Run the seed script (from `server/`: `yarn run db:seed:test`)

## Redis setup

1. Ensure `/dev/redis/data` folder exists
2. From `dev/`, run: `docker compose -p wisdom up redis`

## Server (backend) setup

- To start the backend API, from `server/` run: `yarn run start`
  - Note that the backend requires MySQL and Redis to be available

```shell
# .env config

DATABASE_READER_URL=mysql://wisdom_reader:wisdom_reader@127.0.0.1:3306/wisdom_dev
DATABASE_WRITER_URL=mysql://wisdom_writer:wisdom_writer@127.0.0.1:3306/wisdom_dev
CONFIG_ENV=development
NODE_ENV=development
PORT=5002
REDIS_URL=redis://localhost:6379
SITE_URL=http://localhost:3000
STAGE=development
STRIPE_SK={{ STRIPE_SK }}
SKIP_RATE_LIMIT=true
```

## Web (frontend) setup

- To start the client, from `web/`:
  1. Build the client: `yarn run next-build`
  2. Start the client: `yarn run next-start`

**Development workflow**:
- For single page/styling work: `yarn run next-dev` (in web directory)
- For testing across multiple pages/UX flows: `yarn run build && yarn run next-start` (in web directory)

```shell
# .env.local config

NEXT_PUBLIC_ALGOLIA_APP_ID= {{ NEXT_PUBLIC_ALGOLIA_APP_ID }}
NEXT_PUBLIC_ALGOLIA_SEARCH_API_KEY= {{ NEXT_PUBLIC_ALGOLIA_SEARCH_API_KEY }}
NEXT_PUBLIC_GRAPHQL_URL=http://localhost:5002/graphql
NEXT_PUBLIC_ROOT_DOMAIN=localhost
NEXT_PUBLIC_STAGE=development
NEXT_PUBLIC_STRIPE_PK= {{ NEXT_PUBLIC_STRIPE_PK }}
NEXT_PUBLIC_STENCIL_BASE_URL=http://localhost:5005
REDIS_URL=redis://localhost:6379
```

## Build (for CI/production only)

- Server: `npm run build:server` (in root, used for Docker containers)
- Web: `npm run build:client` (in root, used for Docker containers)

## Testing

- Server: `yarn test` or `yarn test:ci` for CI environment
- Web: `yarn test` or `yarn test:ci` for CI environment

## Copilot Agent

- Use `./scripts/yarn-copilot.sh` instead of `yarn` when working in Copilot Agent environments where environment variables like `FONT_AWESOME_TOKEN` and `GITHUB_TOKEN` are not available
- Example: `./scripts/yarn-copilot.sh test` instead of `yarn test`

## E2E testing notes

- To run the E2E tests, have `MySQL`, `Redis`, `server`, and `web` running
  - Run via CLI, from `web/`: `yarn run playwright:run:ci`
  - Run via GUI, from `web/`: `npx playwright test --ui`
  - Legacy: Run via CLI, from `web/`: `yarn run cypress:run:ci`
  - Legacy: Run via GUI, from `web/`: `npx cypress open`
    - Select `E2E Testing`
    - Select `Electron`
- Note that ispecs perform database CRUD operations (database refreshes may be needed to restore data to initial state, depending on the ispec)
- Stripe has limits on the number of test customers (and this may be encountered when running the ispecs), to delete test customers, run the following script from `server/` codebase:
  - `npx ts-node ./scripts/delete-stripe-test-customers.ts`

# Repository Structure

- `packages/`: Main monorepo structure with multiple packages
  - `server/`: Backend server with Apollo GraphQL
    - `src/`: Main server code
    - `migrations/`: Database migrations
    - `seeders/`: Database seed files
    - `seeders-test/`: Test seed data (used for local development and E2E testing)
    - `scripts/`: Utility scripts
  - `web/`: Frontend Next.js application
    - `src/`: Legacy React code
    - `app/`: Next.js App Router code
    - `cypress/`: Legacy E2E tests
    - `playwright/`: E2E tests (current testing framework)
  - `tundra-wizard/`: Shared utilities package
  - `dev/`: Development utilities and scripts
  - `cloudflare/`: Cloudflare related code

# Database Schema Overview

The database consists of the following tables that support the learning platform:

- `accessCodes`: Access codes for course enrollment and special access
- `affiliatePayments`: Payment records for affiliate commissions
- `affiliates`: Affiliate partner information and tracking
- `amazonGiftCards`: Amazon gift card rewards and redemption tracking
- `auditLogs`: System audit trail and activity logging
- `authorPayments`: Payment records for content authors
- `authors`: Content author information and profiles
- `awardNominations`: Award and recognition nominations
- `bookmarks`: User bookmarks for content and questions
- `categories`: Content categorization and organization
- `charges`: Payment and billing transaction records
- `configVars`: System configuration variables
- `contentLogs`: Content access and interaction logging
- `contents`: Individual learning content units within products
- `contentSlugs`: URL slug mappings for content
- `contentsMeta`: Metadata for content items
- `emailRecords`: Email communication tracking and history
- `enumeratedExamItems`: Pre-defined exam question sets
- `essayPrompts`: Essay question prompts and templates
- `essayResponses`: User essay submissions and responses
- `events`: System events and activity tracking
- `examContents`: Specialized content for practice exams
- `exams`: Exam definitions and configurations
- `externalExamItems`: External exam question imports
- `externalExams`: External exam integrations
- `feedback`: User feedback and survey responses
- `feedbackData`: Structured feedback data and analytics
- `flaggedItems`: Content items flagged for review
- `grants`: Educational grants and funding tracking
- `items`: Individual questions/learning items within content
- `itemsExports`: Data exports of learning items
- `itemsMeta`: Metadata for individual learning items
- `memoryEstimates`: Spaced repetition algorithm data for learning optimization
- `organizationPricings`: Pricing configurations for organizations
- `organizations`: Groups/institutions that users belong to
- `orgEmailRegexes`: Email domain patterns for organization assignment
- `presentations`: User interactions with learning items (attempts, timing)
- `productFiles`: Files and resources associated with products
- `products`: Learning products/courses (e.g., exam prep materials)
- `promoCodes`: Discount codes and promotional offers
- `referrals`: User referral tracking and rewards
- `scholarshipApplications`: Scholarship application submissions
- `SequelizeData`: Database migration tracking (Sequelize framework)
- `SequelizeMeta`: Database migration metadata (Sequelize framework)
- `slices`: Content organization and grouping
- `sliceSubscriptions`: Subscription access to content slices
- `studyPlans`: Personalized study plans and schedules
- `subscriptions`: User subscription records and access
- `tasks`: Background task queue and processing
- `testimonials`: User testimonials and success stories
- `tutorChats`: Tutoring chat sessions and messages
- `tutorThreads`: Tutoring conversation threads
- `userAccounts`: User account management and settings
- `userContents`: User progress tracking for content completion
- `userExamItems`: Individual question responses within practice exams
- `userExams`: Practice exam sessions and results
- `userExternalExamItems`: User responses to external exam questions
- `userExternalExams`: User external exam session records
- `userReadings`: User reading progress and tracking
- `users`: User accounts and authentication data
- `videos`: Video content and streaming information

# Code Style Guidelines

## Required Before Each Commit

- Run `yarn lint` to ensure code quality and consistent style
- For web package: Run `yarn next-lint` for Next.js specific linting
- Use Prettier for consistent code formatting (configuration is in `.prettierrc.js` files)
- Remove any trailing spaces (non-space whitespace is likely intentional and should be preserved)
- Ensure all files end with a newline

## Development Guidelines

1. Adhere to TypeScript best practices, ensuring consistent and precise type definitions across the codebase.
2. Preserve the existing code structure and organization to maintain readability and ease of collaboration.
3. Use GraphQL with Apollo Client for all data-fetching operations, following established patterns in the codebase.
4. Write comprehensive unit tests for all new functionality to ensure reliability and prevent regressions.
5. Follow the component structure already established in the codebase to maintain consistency and reusability.
6. Use environment variables appropriately for different environments (development, test, UAT, production), ensuring sensitive data is never hardcoded.
7. Follow the established import order conventions for better readability:
   - Node.js built-ins first
   - External libraries next
   - Internal modules and components
   - Styles last
8. Maintain consistent styling by adhering to the existing patterns for styled components or CSS modules.
9. Do not modify the database schema without creating and applying the corresponding migration files.
10. Prioritize accessibility when developing UI components to ensure the platform is usable by all users.
11. Prefer using functional components and hooks over class components
12. Use `async/await` for asynchronous code instead of callbacks or `.then()`
13. Use `const` and `let` instead of `var`
14. Use `===` and `!==` for equality checks instead of `==` and `!=`
15. Use template literals for string concatenation
16. Avoid using `any` type in TypeScript; use specific types or generics instead
17. Use `null` and `undefined` appropriately; avoid using them interchangeably
18. Prefer using `import` statements over `require()` for module imports
19. Prefer switch statements over if-else chains for multiple conditions
20. GraphQL resolvers should generally return a Promise or an async function instead of directly awaiting the result
21. Avoid chaining function calls excessively; instead, break them into separate, well-named variables or functions for improved readability and maintainability.
22. Design components to be simple and modular, ensuring they handle a single responsibility and can be easily reused across the codebase.
23. Document any significant changes or additions to the codebase to help other contributors understand your work.
24. Regularly run linting and formatting tools (`yarn lint`, `yarn next-lint`, and Prettier) to ensure code quality and consistency.

## Function Parameter Patterns

**Preferred**: Functions should receive a single typed object as a parameter with named keys

```typescript
// ✅ Good - Single object parameter with named keys
interface ProcessItemOptions {
	item: DatabaseItem;
	config: ProcessingConfig;
}

const processItem = async ({ item, config }: ProcessItemOptions) => {
	// Implementation
};

// ✅ Good - Object destructuring with clear interface
const spellcheckProduct = async ({ productUuid, exam }: SpellcheckProductOptions) => {
	// Implementation
};
```

**Avoid**: Multiple separate parameters

```typescript
// ❌ Avoid - Multiple separate parameters
const processItem = async (item: DatabaseItem, config: ProcessingConfig) => {
	// Implementation
};

// ❌ Avoid - Multiple primitive parameters
const spellcheckProduct = async (productUuid: string, exam: string) => {
	// Implementation
};
```

**Benefits of object parameters:**

- **Self-documenting**: Named keys make function calls more readable
- **Extensible**: Easy to add new parameters without breaking existing calls
- **Type-safe**: TypeScript interfaces provide compile-time validation
- **Flexible**: Optional parameters and default values are easier to manage
- **Maintainable**: Refactoring is safer with fewer breaking changes

## Copy Writing and Text Guidelines

1. Use sentence case for UX copy ensuring only the first word and proper nouns are capitalized to maintain a consistent and user-friendly tone.
2. Acroynms and abbreviations should be lowercased when incorporated into variable names, e.g. `userUuid` not `userUUID`

# Notes

2022-03-14: Exams create presentations as well as userExamItems. Before, only userExamItems were created.

# Running scripts with more memory

node --max-old-space-size=8192 -- node_modules/ts-node/dist/bin.js -P tsconfig.json scripts/create-missing-memory-estimates.ts

# Useful queries

```
# Get count of correct, incorrect presentations for a user
select isCorrect, count(*) from presentations inner join users on users.uuid = presentations.userUuid where email like "<EMAIL>" group by presentations.isCorrect;

# Get count of correct, incorrect presentations by content for a user
select contents.name, isCorrect, count(*) from presentations inner join items on presentations.itemUuid = items.uuid inner join contents on contents.uuid = items.contentUuid inner join users on users.uuid = presentations.userUuid where email like "<EMAIL>" group by contents.uuid, presentations.isCorrect;

# Select duplicate memoryEstimates for a user
# THIS IS SLOW; DON'T RUN IT ON PROD
select * from memoryEstimates where userUuid = "a753b36c-35c0-41a9-99d7-cf269a115514" and itemUuid in (select itemUuid from memoryEstimates where userUuid = 'a753b36c-35c0-41a9-99d7-cf269a115514' group by itemUuid having count(itemUuid) > 1)

# Select duplicate memoryEstimates for all users
# THIS IS SLOW; DON'T RUN IT ON PROD
select * from memoryEstimates inner join (select itemUuid, userUuid from memoryEstimates group by itemUuid, userUuid having count(*) > 1) grouped on grouped.userUuid = memoryEstimates.userUuid and grouped.itemUuid = memoryEstimates.itemUuid order by memoryEstimates.userUuid, memoryEstimates.itemUuid

# Select orphaned items
select * from items left outer join (select uuid, name from contents union select uuid, name from examContents) c on items.contentUuid = c.uuid where contentUuid not like "gre--%" and c.uuid is null order by items.createdAt, items.contentUuid asc

# Sum presentation time by user for a product
select sum(totalMs), userUuid from presentations inner join items on items.uuid = presentations.itemUuid inner join contents on contents.uuid = items.contentUuid inner join products on products.uuid = contents.productUuid where products.uuid = '7458c278-9b47-4c6a-aac6-78d25df2a95f' group by userUuid

# Get content uuids for an exam
select contents.uuid from contents inner join products on contents.productUuid = products.uuid where products.slug = 'finra-series-7'

# Get items for an exam
select items.uuid from items where items.contentUuid in (select contents.uuid from contents inner join products on contents.productUuid = products.uuid where products.slug = 'finra-series-7')

# Update userContents for a user and exam
update userContents set completedAt = null, updatedAt = NOW() where userContents.contentUuid in (select contents.uuid from contents inner join products on contents.productUuid = products.uuid where products.slug = 'finra-series-7') and userContents.userUuid = '7826e258-018c-4883-a60f-ab8f693827f0'

# Delete memory estimates for a user and exam
delete from memoryEstimates where memoryEstimates.itemUuid in (select items.uuid from items where items.contentUuid in (select contents.uuid from contents inner join products on contents.productUuid = products.uuid where products.slug = 'finra-series-7')) and memoryEstimates.userUuid = '7826e258-018c-4883-a60f-ab8f693827f0'

# Delete practice exam record for a user and exam
delete from userExams where productSlug = 'finra-series-7' and userUuid = '89eea7ba-fd89-4607-a5c7-0be85c21c85e'

# Count repeat incorrect exam questions for a user
select count(*), userExamItems.itemUuid, itemEvaluated from userExamItems inner join userExams on userExams.uuid = userExamItems.userExamUuid inner join users on users.uuid = userExams.userUuid where users.email = '<EMAIL>' and userExamItems.status='incorrect' group by userExamItems.itemUuid

# Find and add unaffiliated evaluation folks to arthur's organization and source
select * from users inner join charges on users.uuid = charges.userUuid inner join promoCodes on promoCodes.uuid = charges.promoCodeUuid where promoCodes.code in ('nm_eval', 'rj_eval', 'pru_eval') and users.organizationUuid is null;
update users inner join charges on users.uuid = charges.userUuid inner join promoCodes on promoCodes.uuid = charges.promoCodeUuid set users.organizationUuid = '51046ffa-06f9-494d-b08c-4de4d014af90' where promoCodes.code in ('nm_eval', 'rj_eval', 'pru_eval') and users.organizationUuid is null;
update users inner join charges on users.uuid = charges.userUuid inner join promoCodes on promoCodes.uuid = charges.promoCodeUuid set users.source = 'arthur' where promoCodes.code in ('nm_eval', 'rj_eval', 'pru_eval') and users.source is null;

# Find orphaned items
select
	items.uuid,
	items.contentUuid,
	code,
	bundle,
	items.createdAt,
	items.updatedAt,
	tbl.name
from
	items
	left outer join (select uuid, name from contents union select uuid, name from examContents) as tbl
on items.contentUuid = tbl.uuid
where tbl.uuid is NULL
and contentUuid not like 'gre-%'
and contentUuid not like 'act-%'
```

### Process for merging content pages

1. Create new content with WIP banner
1. Update old content with deprecated banner
1. Finish new content and remove WIP banner
1. Code redirects from old content slugs to new content slug
1. Code redirects from old content uuids to new content slug
1. Update items.contentUuid from old content uuid to new content uuid
1. Delete old content and empty parents
1. Deploy redirects

# Buildkite

```
export secrets_bucket=buildkite-managedsecretsbucket-xqhyrammspb7

aws s3 cp --profile buildkite --acl private --sse aws:kms id_rsa_buildkite "s3://${secrets_bucket}/private_ssh_key"

aws s3 cp --profile buildkite --acl private --sse aws:kms .buildkite/.env "s3://${secrets_bucket}/environment"
```

# Other

```
brew install mysql-client blackbox
```

Bump: 0.0.8
