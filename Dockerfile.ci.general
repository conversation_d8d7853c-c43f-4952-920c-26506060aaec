FROM arm64v8/ubuntu:24.04
SHELL ["/bin/bash", "-c"]

ARG GITHUB_TOKEN $GITHUB_TOKEN
ARG FONT_AWESOME_TOKEN $FONT_AWESOME_TOKEN
ARG NODE_ENV $NODE_ENV
ENV GITHUB_TOKEN $GITHUB_TOKEN
ENV FONT_AWESOME_TOKEN $FONT_AWESOME_TOKEN
ENV AWS_ACCESS_KEY_ID $AWS_ACCESS_KEY_ID
ENV AWS_SECRET_ACCESS_KEY $AWS_SECRET_ACCESS_KEY
ENV AWS_DEFAULT_REGION $AWS_DEFAULT_REGION
ENV AWS_REGION $AWS_REGION

ENV NVM_DIR /root/.nvm
ENV NODE_VERSION 20.18.1
ENV NODE_PATH $NVM_DIR/versions/node/v$NODE_VERSION/lib/node_modules
ENV PATH $NVM_DIR/versions/node/v$NODE_VERSION/bin:$PATH

RUN env

USER root
RUN mkdir /app
WORKDIR /app

RUN apt-get -o Acquire::Check-Valid-Until=false --allow-releaseinfo-change update

RUN apt-get install -y \
	build-essential \
	ca-certificates \
	curl \
	curl \
	default-mysql-client \
	git \
	gnupg \
	jq \
	libasound2t64 \
	libgbm-dev \
	libgtk-3-0 \
	libgtk2.0-0 \
	libnotify-dev \
	libnss3 \
	libxss1 \
	libxtst6 \
	lsb-release \
	m4 \
	pip \
	python-is-python3 \
	python3-pip \
	python3-venv \
	unzip \
	xauth \
	xvfb \
	zip

RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash \
	&& . $NVM_DIR/nvm.sh \
	&& nvm install $NODE_VERSION \
	&& nvm alias default $NODE_VERSION \
	&& nvm use default

RUN node --version
RUN npm --version
RUN npm install -g corepack@0.24.1
RUN corepack enable && corepack prepare yarn@4.5.1 --activate
RUN yarn --version

RUN git clone https://github.com/aws/aws-elastic-beanstalk-cli.git
WORKDIR /app/aws-elastic-beanstalk-cli

# aws-elastic-beanstalk v3.21.0
RUN git checkout 5dd34def398a223554c3980b741d15f251f6e605

RUN pip install --break-system-packages --no-build-isolation pyyaml==5.3.1
RUN pip install --break-system-packages .

RUN echo 'export PATH="/app/aws-elastic-beanstalk-cli/bin:$PATH"' >> ~/.bash_profile && . ~/.bash_profile
ENV PATH="/app/aws-elastic-beanstalk-cli/bin:$PATH"

RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip" -o "awscliv2.zip"
RUN unzip awscliv2.zip
RUN ./aws/install
RUN aws --version

RUN mkdir -p /etc/apt/keyrings
RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg
RUN echo \
	"deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
	$(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
RUN apt-get -o Acquire::Check-Valid-Until=false --allow-releaseinfo-change update
RUN apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

WORKDIR /app/packages/server
COPY .npmrc packages/server/package.json packages/server/.yarnrc.yml packages/server/yarn.lock /app/packages/server/
RUN yarn install --immutable

WORKDIR /app/packages/web
COPY .npmrc packages/web/package.json packages/web/.yarnrc.yml packages/web/yarn.lock /app/packages/web/
RUN yarn install --immutable
RUN yarn playwright install --with-deps

WORKDIR /app
COPY .npmrc package.json .yarnrc.yml yarn.lock /app/
RUN yarn install --immutable

WORKDIR /app
COPY . /app/
