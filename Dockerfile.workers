FROM node:20.18 as build-server
ARG GITHUB_TOKEN
ARG FONT_AWESOME_TOKEN

RUN corepack enable && corepack prepare yarn@4.5.1 --activate

WORKDIR /usr/src/app/server
COPY .npmrc packages/server/package.json packages/server/.yarnrc.yml packages/server/yarn.lock ./
RUN yarn install --immutable
RUN rm -rf .npmrc

WORKDIR /usr/src/app/server
COPY packages/server ./
RUN npm run build

######################################

FROM node:20.18 as dist
ARG GITHUB_TOKEN
ARG FONT_AWESOME_TOKEN

RUN corepack enable && corepack prepare yarn@4.5.1 --activate

WORKDIR /usr/src/app
COPY .npmrc packages/server/package.json packages/server/.yarnrc.yml packages/server/yarn.lock ./
RUN yarn install --immutable
RUN rm -rf .npmrc

COPY --from=build-server /usr/src/app/server/dist/src ./src
COPY packages/server/IMAGE ./IMAGE
COPY packages/server/tsconfig.json ./tsconfig.json
COPY packages/server/src/emails/partials ./src/emails/partials
COPY packages/server/src/emails/templates ./src/emails/templates
COPY packages/server/src/services/spellcheckService/en_US.aff packages/server/src/services/spellcheckService/en_US.dic packages/server/src/services/spellcheckService/achievable.dic ./src/services/spellcheckService/
COPY packages/server/src/views ./src/views
COPY packages/server/pdfs-master ./pdfs-master

RUN apt-get update && apt-get install -y \
	ghostscript \
	&& rm -rf /var/lib/apt/lists/*

EXPOSE 80
CMD [ "npm", "run", "workers:production" ]
# CMD [ "npm", "run", "maintenance" ]
