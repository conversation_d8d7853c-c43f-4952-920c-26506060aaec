name: "Copilot Setup Steps"

# Allow testing of the setup steps from your repository's "Actions" tab.
on: workflow_dispatch

jobs:
  # The job MUST be called copilot-setup-steps or it will not be picked up by Copilot.
  copilot-setup-steps:
    runs-on: ubuntu-latest

    # Set the permissions to the lowest permissions possible needed for your steps.
    permissions:
      # Read access to repository content is needed to checkout the code
      contents: read

    env:
      COPILOT_AGENT_FIREWALL_ALLOW_LIST_ADDITIONS: api.nektosact.com
      FONT_AWESOME_TOKEN: ${{ secrets.FONT_AWESOME_TOKEN }}
      GITHUB_TOKEN: ${{ secrets.ACHIEVABLE_GITHUB_TOKEN }}

    # Set up steps for the Copilot agent
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.18.1"
          cache: "yarn"
        env:
          GITHUB_TOKEN: ${{ secrets.ACHIEVABLE_GITHUB_TOKEN }}
          FONT_AWESOME_TOKEN: ${{ secrets.FONT_AWESOME_TOKEN }}

      # Install root dependencies first
      - name: Install root dependencies
        run: yarn install
        env:
          GITHUB_TOKEN: ${{ secrets.ACHIEVABLE_GITHUB_TOKEN }}
          FONT_AWESOME_TOKEN: ${{ secrets.FONT_AWESOME_TOKEN }}

      # Setup for packages/server
      - name: Setup server package
        working-directory: packages/server
        run: |
          yarn install
          yarn build
        env:
          GITHUB_TOKEN: ${{ secrets.ACHIEVABLE_GITHUB_TOKEN }}
          FONT_AWESOME_TOKEN: ${{ secrets.FONT_AWESOME_TOKEN }}

      # Setup for packages/web
      - name: Setup web package
        working-directory: packages/web
        run: yarn install
        env:
          GITHUB_TOKEN: ${{ secrets.ACHIEVABLE_GITHUB_TOKEN }}
          FONT_AWESOME_TOKEN: ${{ secrets.FONT_AWESOME_TOKEN }}

      # Setup for packages/tundra-wizard
      - name: Setup tundra-wizard package
        working-directory: packages/tundra-wizard
        run: yarn install
        env:
          GITHUB_TOKEN: ${{ secrets.ACHIEVABLE_GITHUB_TOKEN }}
          FONT_AWESOME_TOKEN: ${{ secrets.FONT_AWESOME_TOKEN }}

      # No setup for packages/dev as per the requirement

      # Verify root project dependencies
      - name: Verify root dependencies
        run: |
          echo "Verifying root dependencies..."
          [ -d "node_modules" ] && echo "✅ Root node_modules exists" || { echo "❌ Root node_modules missing"; exit 1; }
        env:
          GITHUB_TOKEN: ${{ secrets.ACHIEVABLE_GITHUB_TOKEN }}
          FONT_AWESOME_TOKEN: ${{ secrets.FONT_AWESOME_TOKEN }}

      # Verify server package
      - name: Verify server package
        working-directory: packages/server
        run: |
          echo "Verifying server package setup..."
          [ -d "node_modules" ] && echo "✅ Server node_modules exists" || { echo "❌ Server node_modules missing"; exit 1; }
        env:
          GITHUB_TOKEN: ${{ secrets.ACHIEVABLE_GITHUB_TOKEN }}
          FONT_AWESOME_TOKEN: ${{ secrets.FONT_AWESOME_TOKEN }}

      # Verify web package
      - name: Verify web package
        working-directory: packages/web
        run: |
          echo "Verifying web package setup..."
          [ -d "node_modules" ] && echo "✅ Web node_modules exists" || { echo "❌ Web node_modules missing"; exit 1; }
        env:
          GITHUB_TOKEN: ${{ secrets.ACHIEVABLE_GITHUB_TOKEN }}
          FONT_AWESOME_TOKEN: ${{ secrets.FONT_AWESOME_TOKEN }}

      # Verify tundra-wizard package
      - name: Verify tundra-wizard package
        working-directory: packages/tundra-wizard
        run: |
          echo "Verifying tundra-wizard package setup..."
          [ -d "node_modules" ] && echo "✅ Tundra-wizard node_modules exists" || { echo "❌ Tundra-wizard node_modules missing"; exit 1; }
        env:
          GITHUB_TOKEN: ${{ secrets.ACHIEVABLE_GITHUB_TOKEN }}
          FONT_AWESOME_TOKEN: ${{ secrets.FONT_AWESOME_TOKEN }}

      - name: Verify environment
        run: |
          echo "Node.js version: $(node --version)"
          echo "Yarn version:"
          yarn --version
          echo "Environment setup complete ✅"
        env:
          GITHUB_TOKEN: ${{ secrets.ACHIEVABLE_GITHUB_TOKEN }}
          FONT_AWESOME_TOKEN: ${{ secrets.FONT_AWESOME_TOKEN }}
