name: CI/CD

on:
  push:
    branches-ignore:
      - deploy

jobs:
  skip-ci:
    runs-on: ubuntu-latest
    if: "contains(github.event.head_commit.message, '[skip ci]')"

    strategy:
      matrix:
        node-version: [18.16]

    steps:
      - run: "true"

  test-server:
    env:
      CI: true
      GITHUB_TOKEN: ${{ secrets.GIT_CI_CD_TOKEN }}
      npm_config_cache: ${{ github.workspace }}/.npm

    runs-on: ubuntu-latest
    if: "!contains(github.event.head_commit.message, '[skip ci]')"

    strategy:
      matrix:
        node-version: [18.16]

    steps:
      - uses: actions/checkout@v1
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: https://npm.pkg.github.com/
          scope: 'achievable-me'
      - run: sudo npm i npm@latest -g
      - name: Cache node modules
        uses: actions/cache@v1
        with:
          path: ${{ github.workspace }}/.npm
          key: ${{ runner.OS }}-server-npm-${{ hashFiles('packages/server/package-lock.json') }}
          restore-keys: |
            ${{ runner.OS }}-server-npm-${{ env.cache-name }}-
            ${{ runner.OS }}-server-npm-
      - run: sudo npm i npm@latest -g
      - run: npm ci --verbose
        working-directory: ${{ github.workspace }}/packages/server
      - run: npm run test:ci
        working-directory: ${{ github.workspace }}/packages/server

  test-web:
    env:
      CI: true
      CYPRESS_CACHE_FOLDER: ${{ github.workspace }}/cache/Cypress
      GITHUB_TOKEN: ${{ secrets.GIT_CI_CD_TOKEN }}
      npm_config_cache: ${{ github.workspace }}/.npm

    runs-on: ubuntu-latest
    if: "!contains(github.event.head_commit.message, '[skip ci]')"

    strategy:
      matrix:
        node-version: [18.16]

    steps:
      - uses: actions/checkout@v1
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: https://npm.pkg.github.com/
          scope: 'achievable-me'
      - name: Cache node modules
        uses: actions/cache@v1
        with:
          path: ${{ github.workspace }}/.npm
          key: ${{ runner.OS }}-web-npm-${{ hashFiles('packages/web/package-lock.json') }}
          restore-keys: |
            ${{ runner.OS }}-web-npm-${{ env.cache-name }}-
            ${{ runner.OS }}-web-npm-
      - name: Cache cypress
        uses: actions/cache@v1
        with:
          path: ${{ github.workspace }}/cache/Cypress
          key: ${{ runner.OS }}-web-cypress-${{ hashFiles('packages/web/package-lock.json') }}
          restore-keys: |
            ${{ runner.OS }}-web-cypress-${{ env.cache-name }}-
            ${{ runner.OS }}-web-cypress-
      - run: sudo npm i npm@latest -g
      - run: npm ci --verbose
        working-directory: ${{ github.workspace }}/packages/web
      - run: npm run test:ci
        working-directory: ${{ github.workspace }}/packages/web

  test-integration:
    env:
      CI: true
      CYPRESS_CACHE_FOLDER: ${{ github.workspace }}/cache/Cypress
      GITHUB_TOKEN: ${{ secrets.GIT_CI_CD_TOKEN }}
      npm_config_cache: ${{ github.workspace }}/.npm

    runs-on: ubuntu-latest
    container:
      image: cypress/browsers:node14.7.0-chrome84
      options: --ipc=host
    if: "!contains(github.event.head_commit.message, '[skip ci]')"

    needs: [test-server, test-web]

    services:
      mysql:
        image: mysql:5.6
        ports:
          - 3306/tcp
        env:
          MYSQL_DATABASE: wisdom_test
          MYSQL_ROOT_PASSWORD: wisdom
        options:
          --health-cmd "/usr/bin/mysql --user=root --password=wisdom --execute \"SHOW DATABASES;\""
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    strategy:
      matrix:
        node-version: [18.16]

    steps:
      - uses: actions/checkout@v1
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: https://npm.pkg.github.com/
          scope: 'achievable-me'
      # - name: Cache node modules
      #   uses: actions/cache@v1
      #   with:
      #     path: ${{ github.workspace }}/.npm
      #     key: ${{ runner.OS }}-integration-npm-${{ hashFiles('packages/**/package-lock.json') }}
      #     restore-keys: |
      #       ${{ runner.OS }}-integration-npm-${{ env.cache-name }}-
      #       ${{ runner.OS }}-integration-npm-
      # - name: Cache cypress
      #   uses: actions/cache@v1
      #   with:
      #     path: ${{ github.workspace }}/cache/Cypress
      #     key: ${{ runner.OS }}-integration-cypress-${{ hashFiles('packages/web/package-lock.json') }}
      #     restore-keys: |
      #       ${{ runner.OS }}-integration-cypress-${{ env.cache-name }}-
      #       ${{ runner.OS }}-integration-cypress-
      - name: install system dependencies
        shell: bash
        run: |
          apt-get -o Acquire::Check-Valid-Until=false --allow-releaseinfo-change update
          apt-get install -y default-mysql-client
      - name: Git config
        run: |
          git config --system user.email "<EMAIL>"
          git config --system user.name "Achievable"
      - name: install root packages
        shell: bash
        run: npm ci --verbose
      - name: install server packages
        shell: bash
        run: npm ci --verbose
        working-directory: ${{ github.workspace }}/packages/server
      - name: install web packages
        shell: bash
        run: npm ci --verbose
        working-directory: ${{ github.workspace }}/packages/web
      - name: install cypress
        shell: bash
        run: |
          DEBUG=cypress:* npx cypress install
          DEBUG=cypress:* npx cypress verify
        working-directory: ${{ github.workspace }}/packages/web
      - name: set up database
        shell: bash
        run: |
          cd packages/dev
          mysql -u root --password=wisdom -h mysql -P 3306 wisdom_test < test-db.sql || true
          cd ../../packages/server
          BECOME_ENV=test npm run db:seed:test
      - name: build
        shell: bash
        run: |
          cd packages/web
          npm run build:test
          rm -rf ../../packages/server/build
          mv build ../../packages/server
      - name: test
        shell: bash
        env:
          DATABASE_URL: mysql://root:wisdom@mysql:3306/wisdom_test?reconnect=true
        run: |
          cd packages/server
          BECOME_ENV=test npm run start:production >server.log 2>&1 &
          npx wait-on http://localhost:8080/status
          cd ../../packages/web
          npm run cypress:run:ci
      - name: Archive server log
        uses: actions/upload-artifact@v1
        with:
          name: server-combined.log
          path: packages/server/combined.log
