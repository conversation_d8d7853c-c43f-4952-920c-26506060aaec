apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  name: achievable-app
spec:
  replicas: 1
  strategy:
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 2
  revisionHistoryLimit: 10
  minReadySeconds: 0
  template:
    metadata:
      labels:
        run: achievable-app
    spec:
      containers:
        - name: achievable-app
          image: __IMAGE__
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: achievable-app-secrets
            - configMapRef:
                name: achievable-app-configmap
          ports:
            - containerPort: 3000
