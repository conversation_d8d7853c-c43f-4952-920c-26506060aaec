apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: achievable-app
  annotations:
    kubernetes.io/ingress.class: "nginx"
    kubernetes.io/ingress.allow-http: "true"
    certmanager.k8s.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
    - hosts:
        - "app--uat.achievable.me"
      secretName: achievable-app-tls
  rules:
    - host: "app--uat.achievable.me"
      http:
        paths:
          - backend:
              serviceName: achievable-app
              servicePort: 80
