#!/bin/bash
set -e
set -o pipefail

EB_ENV="${EB_ENV:-}"

if [ -z "$EB_ENV" ]; then
  echo "Error: EB_ENV is not set. Please set it as an environment variable."
  echo "Example: EB_ENV=wisdom--workers--uat--lime ./deploy--wisdom--workers.sh"
  exit 1
fi

echo "Deploying to EB_ENV: $EB_ENV"

rm -rf dist/*

ECR_REPO=964996960114.dkr.ecr.us-west-2.amazonaws.com
VERSION=`npx -c 'echo "$npm_package_version"'`

aws ecr get-login-password | docker login --username AWS --password-stdin 964996960114.dkr.ecr.us-west-2.amazonaws.com

APP_NAME_WORKERS=wisdom--workers

PORT_WORKERS=80

ECR_NAME_WORKERS=wisdom-workers

IMAGE_WORKERS=$ECR_NAME_WORKERS:$VERSION-$STAGE

m4 -DECR_REPO="$ECR_REPO" -DIMAGE="$IMAGE_WORKERS" -DPORT="$PORT_WORKERS" Dockerrun.aws.template.json > Dockerrun.aws.json
m4 -DSTAGE="$STAGE" -DAPP_NAME="$APP_NAME_WORKERS" .ebextensions/newrelic.config.template > .ebextensions/newrelic.config
zip -r dist/dist--workers.zip Dockerrun.aws.json .platform .ebextensions

m4 -DAPP_NAME="wisdom--workers" -DARTIFACT="dist/dist--workers.zip" .elasticbeanstalk/config.yml.template > .elasticbeanstalk/config.yml

if [ -z "$CI" ] ; then
    eb deploy $EB_ENV --staged --timeout 30 --profile achievable
else
    eb deploy $EB_ENV --staged --timeout 30
fi
