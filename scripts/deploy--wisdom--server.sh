#!/bin/bash
set -e
set -o pipefail

rm -rf dist/*

ECR_REPO=964996960114.dkr.ecr.us-west-2.amazonaws.com
VERSION=`npx -c 'echo "$npm_package_version"'`

aws ecr get-login-password | docker login --username AWS --password-stdin 964996960114.dkr.ecr.us-west-2.amazonaws.com

APP_NAME=wisdom

PORT_SERVER=3000

ECR_NAME=wisdom-server

IMAGE=$ECR_NAME:$VERSION-$STAGE

m4 -DECR_REPO="$ECR_REPO" -DIMAGE="$IMAGE" -DPORT="$PORT_SERVER" Dockerrun.aws.template.json > Dockerrun.aws.json
m4 -DSTAGE="$STAGE" -DAPP_NAME="$APP_NAME" .ebextensions/newrelic.config.template > .ebextensions/newrelic.config
zip -r dist/dist--server.zip Dockerrun.aws.json .platform .ebextensions

m4 -DAPP_NAME="wisdom" -DARTIFACT="dist/dist--server.zip" .elasticbeanstalk/config.yml.template > .elasticbeanstalk/config.yml

case "$STAGE" in
  production) EB_ENV="wisdom--production--apple" ;;
         uat) EB_ENV="wisdom--uat--lemon" ;;
esac

if [ -z "$CI" ] ; then
    eb deploy $EB_ENV --staged --timeout 30 --profile achievable
else
    eb deploy $EB_ENV --staged --timeout 30
fi
