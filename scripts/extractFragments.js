const fs = require('fs');

const axios = require('axios');

const extractFragments = async () => {
	try {
		const response = await axios({
			url: 'http://localhost:5002/graphql',
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			data: JSON.stringify({
				variables: {},
				query: `
      {
        __schema {
          types {
            kind
            name
            possibleTypes {
              name
            }
          }
        }
      }
    `,
			}),
		});

		const { data } = response.data;

		const filteredData = data.__schema.types.filter((type) => type.possibleTypes !== null);

		data.__schema.types = filteredData;
		fs.writeFile('./packages/web/src/fragmentTypes.json', JSON.stringify(data), (err) => {
			if (err) {
				console.error('Error writing fragmentTypes file', err);
			} else {
				console.log('Fragment types successfully extracted!');
			}
		});
	} catch (e) {
		console.log(e);
		throw e;
	}
};

extractFragments();
