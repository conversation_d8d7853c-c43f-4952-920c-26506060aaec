#!/bin/bash
set -e

# This script provides stub environment variables for running yarn commands
# in the Copilot Agent environment where FONT_AWESOME_TOKEN and GITHUB_TOKEN
# are not available.
# It includes guardrails to warn about commands that may require real authentication.

# Define which yarn commands are known to require real authentication
UNSAFE_COMMANDS=(
    "add"
    "upgrade" 
    "publish"
    "login"
    "pack"
    "version"
    "link"
    "unlink"
    "global"
)

# List of known private package patterns that likely need authentication
PRIVATE_PACKAGE_PATTERNS=(
    "@fortawesome/pro-"
    "@achievable-me/"
)

# Function to check if a command is safe to run in Copilot environment
check_command_safety() {
    local cmd="$1"
    shift
    local args="$*"
    
    # Check against known unsafe commands
    for unsafe_cmd in "${UNSAFE_COMMANDS[@]}"; do
        if [[ "$cmd" == "$unsafe_cmd" ]]; then
            echo "  WARNING: 'yarn $cmd' may require real authentication tokens"
            echo "   This command might fail in Copilot Agent environment"
            echo "   Consider running this in a proper CI environment instead"
            echo ""
            return 1
        fi
    done
    
    # Special case: yarn add with scoped packages or specific registries
    if [[ "$cmd" == "add" ]] || [[ "$cmd" == "upgrade" ]]; then
        for pattern in "${PRIVATE_PACKAGE_PATTERNS[@]}"; do
            if [[ "$args" =~ $pattern ]]; then
                echo "  WARNING: Package '$pattern*' likely requires authentication"
                echo "   Installing or upgrading private packages will fail in Copilot Agent environment"
                echo ""
                return 1
            fi
        done
        
        # Generic check for any scoped package
        if [[ "$args" =~ @.*/ ]]; then
            echo "  WARNING: Adding/upgrading scoped packages may require authentication"
            echo "   If these packages are from private registries, this will fail"
            echo ""
            return 1
        fi
    fi
    
    return 0
}

# Function to analyze yarn output and provide helpful error messages when auth failures occur
detect_auth_failure() {
    local exit_code=$1
    local output_file=$2
    
    if [[ $exit_code -ne 0 ]]; then
        # Check for common authentication error patterns
        if grep -q -i -E "(401|unauthorized|authentication|forbidden|invalid.*token|login.*required)" "$output_file"; then
            echo ""
            echo " AUTHENTICATION ERROR DETECTED:"
            echo "   This command failed because it tried to access private resources"
            echo "   that require real FONT_AWESOME_TOKEN or GITHUB_TOKEN values."
            echo ""
            echo "   Solutions:"
            echo "   1. Run this command in a GitHub Actions workflow where secrets are available"
            echo "   2. Use 'yarn install' instead if dependencies are already in yarn.lock"
            echo "   3. Consider if this operation is necessary for the current task"
            echo ""
            return 1
        fi
    fi
    return 0
}

# Print safe commands that can be used in Copilot environment
print_safe_commands() {
    echo "Commands typically safe to use in Copilot environment:"
    echo "  - yarn install"
    echo "  - yarn build"
    echo "  - yarn test"
    echo "  - yarn lint"
    echo "  - yarn start"
    echo "  - yarn run [script]"
    echo ""
}

# Validate that the script is called with arguments
YARN_CMD="${1:-}"

if [[ -z "$YARN_CMD" ]]; then
    echo "Usage: $0 [yarn_command] [args...]"
    echo "Example: $0 install"
    echo "Example: $0 build"
    echo "Example: $0 test"
    echo ""
    echo "This script provides stub environment variables for yarn commands"
    echo "in environments where FONT_AWESOME_TOKEN and GITHUB_TOKEN are not available."
    echo ""
    print_safe_commands
    exit 1
fi

# Set stub environment variables if they're not already set
if [ -z "$FONT_AWESOME_TOKEN" ]; then
  export FONT_AWESOME_TOKEN="dummy-token-for-copilot"
  echo "Set stub FONT_AWESOME_TOKEN environment variable"
fi

if [ -z "$GITHUB_TOKEN" ]; then
  export GITHUB_TOKEN="dummy-token-for-copilot"
  echo "Set stub GITHUB_TOKEN environment variable"
fi

# Create a temporary file for capturing output
TEMP_OUTPUT=$(mktemp)
trap 'rm -f "$TEMP_OUTPUT"' EXIT

# Check if the command is likely to be safe
check_command_safety "$@"
SAFETY_CHECK=$?

if [[ $SAFETY_CHECK -ne 0 ]]; then
    echo "Running potentially unsafe yarn command with stub environment variables..."
    echo "Proceeding anyway, but be prepared for possible authentication errors."
else
    echo "Running yarn command with stub environment variables..."
fi

echo " Running: yarn $*"
echo ""

# Run yarn command and capture both stdout and stderr
set +e  # Temporarily disable exit on error to capture the exit code
yarn "$@" 2>&1 | tee "$TEMP_OUTPUT"
YARN_EXIT_CODE=$?
set -e

# Check if failure was due to authentication
detect_auth_failure $YARN_EXIT_CODE "$TEMP_OUTPUT"

exit $YARN_EXIT_CODE