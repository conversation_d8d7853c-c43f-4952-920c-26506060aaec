#!/bin/bash
set -e
set -o pipefail

echo "Running smoke tests"

VERSION="$(jq -r '.version' package.json)"

case "$TARGET" in
  production) SERVER="https://api.achievable.me" ;;
         uat) SERVER="-u achievable:achievable https://api--uat.achievable.me" ;;
        test) SERVER="http://wisdom-server:8080" ;;
esac

case "$TARGET" in
  production) IMAGE_SUFFIX="${VERSION}-${TARGET}" ;;
         uat) IMAGE_SUFFIX="${VERSION}-${TARGET}" ;;
        test) IMAGE_SUFFIX="ci-$(git rev-parse HEAD)" ;;
esac

STATUS=$(curl -s $SERVER/status)
STATUS_GREP=$(echo "$STATUS" | grep -o "$IMAGE_SUFFIX" || true)

if [[ -z "$STATUS_GREP" ]]
then
  echo "ERROR: Smoke tests failed server version check"
  echo "Found: ${STATUS}"
  echo "Wanted: ${IMAGE_SUFFIX}"
  exit 1
fi

echo "Smoke tests passed"
