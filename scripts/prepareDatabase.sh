#!/bin/bash
set -e
set -o pipefail

env
node --version
npm --version
yarn --version

echo "nvm bin"
ls /root/.nvm/versions/node/v20.18.1/bin

echo "node_modules bin"
ls node_modules/.bin

cd /app

cd packages/dev
MYSQL_PWD=wisdom mysql -u root -h mysql wisdom_test < test-db.sql
cd -

cd packages/server
BECOME_ENV=test npm run db:seed:test
# npm install sequelize@6.29.0
# npm install sequelize-cli@6.6.0
# NODE_ENV=test /root/.nvm/versions/node/v20.18.1/bin/sequelize db:seed:all --seeders-path=seeders-test --debug
# NODE_ENV=test node_modules/.bin/sequelize db:seed:all --seeders-path=seeders-test --debug
cd -
