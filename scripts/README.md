# Yarn Copilot Script

This script provides a wrapper around yarn commands to make them work in environments where the required environment variables like `FONT_AWESOME_TOKEN` and `GITHUB_TOKEN` are not available, such as in the Copilot Agent.

## Usage

Instead of running `yarn` commands directly, use the script as a prefix:

```bash
# Instead of:
yarn test

# Use:
./scripts/yarn-copilot.sh test
```

The script:

1. Sets dummy values for the required environment variables if they're not already set
2. Runs the yarn command with these variables
3. Passes all arguments to yarn

## Why it's needed

The `.yarnrc.yml` files in this repository reference environment variables like `FONT_AWESOME_TOKEN` and `GITHUB_TOKEN` for authentication with private npm registries. When these variables are not available, yarn commands fail.

This script provides a simple workaround by setting dummy values for these variables, allowing yarn commands to run in environments where these tokens are not needed for the specific command being run.
