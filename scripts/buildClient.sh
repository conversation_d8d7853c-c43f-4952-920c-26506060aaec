#!/bin/bash
set -e
set -o pipefail

echo "BUILD ENV"
env

force=false

while getopts 'f' flag; do
  case "${flag}" in
    f) force=true ;;
  esac
done

ECR_REPO=964996960114.dkr.ecr.us-west-2.amazonaws.com
VERSION=`npx -c 'echo "$npm_package_version"'`

if [ -z "$CI" ] ; then
  aws ecr get-login-password --profile achievable | docker login --username AWS --password-stdin $ECR_REPO
else
  aws ecr get-login-password | docker login --username AWS --password-stdin $ECR_REPO
fi

ECR_NAME=wisdom-server
IMAGE=$ECR_NAME:ci-client-$BUILDKITE_COMMIT
DOCKERFILE=Dockerfile.client

echo "Building $IMAGE"

echo "$IMAGE" > packages/web/IMAGE.client

if [ -z "$CI" ] && [[ "$(docker images -q $IMAGE 2> /dev/null)" != "" ]] ; then
  if ! $force; then
    echo "Image exists, exiting."
    exit 1
  fi
fi


DOCKER_BUILDKIT=0 docker buildx build -f $DOCKERFILE -t $IMAGE --build-arg STAGE=$STAGE --build-arg GITHUB_TOKEN=$GITHUB_TOKEN --build-arg FONT_AWESOME_TOKEN=$FONT_AWESOME_TOKEN --build-arg GITHUB_TOKEN=$GITHUB_TOKEN --build-arg VERSION=$VERSION --platform linux/arm64 .

docker tag $IMAGE $ECR_REPO/$IMAGE
docker push $ECR_REPO/$IMAGE

exit 0
