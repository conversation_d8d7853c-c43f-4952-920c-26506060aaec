#!/bin/bash
set -e
set -o pipefail

echo "BUILD ENV"
env

force=false

while getopts 'f' flag; do
  case "${flag}" in
    f) force=true ;;
  esac
done

rm -rf dist/*

ECR_REPO=964996960114.dkr.ecr.us-west-2.amazonaws.com
VERSION=`npx -c 'echo "$npm_package_version"'`

env

if [ -z "$CI" ] ; then
  aws ecr get-login-password --profile achievable | docker login --username AWS --password-stdin $ECR_REPO
else
  aws ecr get-login-password | docker login --username AWS --password-stdin $ECR_REPO
fi

APP_NAME=wisdom
APP_NAME_WORKERS=wisdom--workers

PORT_SERVER=3000
PORT_WORKERS=80

ECR_NAME=wisdom-server
ECR_NAME_WORKERS=wisdom-workers

if [ "$STAGE" == "test" ] ; then
  IMAGE=$ECR_NAME:ci-$BUILDKITE_COMMIT
  IMAGE_WORKERS=$ECR_NAME_WORKERS:ci-$BUILDKITE_COMMIT
else
  IMAGE=$ECR_NAME:$VERSION-$STAGE
  IMAGE_WORKERS=$ECR_NAME_WORKERS:$VERSION-$STAGE
fi

DOCKERFILE=Dockerfile
DOCKERFILE_WORKERS=Dockerfile.workers

echo "Building $IMAGE"

echo "$IMAGE" > packages/server/IMAGE

if [ -z "$CI" ] && [[ "$(docker images -q $IMAGE 2> /dev/null)" != "" ]] ; then
  if ! $force; then
    echo "Image exists, exiting."
    exit 1
  fi
fi

DOCKER_BUILDKIT=0 docker buildx build -f $DOCKERFILE -t $IMAGE --build-arg STAGE=$STAGE --build-arg GITHUB_TOKEN=$GITHUB_TOKEN --build-arg FONT_AWESOME_TOKEN=$FONT_AWESOME_TOKEN --platform linux/arm64 .
DOCKER_BUILDKIT=0 docker buildx build -f $DOCKERFILE_WORKERS -t $IMAGE_WORKERS --build-arg STAGE=$STAGE --build-arg GITHUB_TOKEN=$GITHUB_TOKEN --build-arg FONT_AWESOME_TOKEN=$FONT_AWESOME_TOKEN --platform linux/arm64 .

docker tag $IMAGE $ECR_REPO/$IMAGE
docker push $ECR_REPO/$IMAGE

docker tag $IMAGE_WORKERS $ECR_REPO/$IMAGE_WORKERS
docker push $ECR_REPO/$IMAGE_WORKERS

if [ "$STAGE" == "test" ] ; then
  exit 0
fi

# mkdir -p $HOME/.ssh
# touch $HOME/.ssh/known_hosts
# ssh-keyscan github.com >> $HOME/.ssh/known_hosts
# git tag "v$VERSION-$STAGE" -f
# git push --tags -f

m4 -DECR_REPO="$ECR_REPO" -DIMAGE="$IMAGE" -DPORT="$PORT_SERVER" Dockerrun.aws.template.json > Dockerrun.aws.json
m4 -DSTAGE="$STAGE" -DAPP_NAME="$APP_NAME" .ebextensions/newrelic.config.template > .ebextensions/newrelic.config
zip -r dist/dist--server.zip Dockerrun.aws.json .platform .ebextensions

m4 -DECR_REPO="$ECR_REPO" -DIMAGE="$IMAGE_WORKERS" -DPORT="$PORT_WORKERS" Dockerrun.aws.template.json > Dockerrun.aws.json
m4 -DSTAGE="$STAGE" -DAPP_NAME="$APP_NAME_WORKERS" .ebextensions/newrelic.config.template > .ebextensions/newrelic.config
zip -r dist/dist--workers.zip Dockerrun.aws.json .platform .ebextensions

rm Dockerrun.aws.json .ebextensions/newrelic.config
