npx license-report --fields=name --fields=license --fields=licensePeriod --fields=link --fields=installedVersion --fields=author --output=html > ~/Downloads/license-report--wisdom.html

cd packages/server
npx license-report --fields=name --fields=license --fields=licensePeriod --fields=link --fields=installedVersion --fields=author --output=html > ~/Downloads/license-report--wisdom-server.html
cd -

cd packages/web
npx license-report --fields=name --fields=license --fields=licensePeriod --fields=link --fields=installedVersion --fields=author --output=html > ~/Downloads/license-report--wisdom-client.html
cd -

cd packages/tundra-wizard
npx license-report --fields=name --fields=license --fields=licensePeriod --fields=link --fields=installedVersion --fields=author --output=html > ~/Downloads/license-report--tundra.html
cd -
