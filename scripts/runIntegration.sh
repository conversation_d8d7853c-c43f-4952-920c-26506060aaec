#!/bin/bash
set -e
set -o pipefail

env
node --version
npm --version
yarn --version

TARGET=test bash scripts/runSmoke.sh

cd /app/packages/web

# Clean up any previous test results
# Don't remove the directory itself as it may be mounted as a volume
mkdir -p test-results
rm -rf test-results/* || true

# Run the tests and preserve results even on failure
yarn run playwright:run:ci || {
  echo "Tests failed. Saving test results for debugging."
  # Ensure permissions are correct for artifact collection
  chmod -R 755 test-results
  exit 1
}
