const pluralize = require("pluralize");

const YEARS_MS = 1000 * 60 * 60 * 24 * 365;
const DAYS_MS = 1000 * 60 * 60 * 24;
const HOURS_MS = 1000 * 60 * 60;
const MINUTES_MS = 1000 * 60;
const MINUTES_SEC = 60;
const SECONDS_MS = 1000;

const humanizeMs = (ms) => {
	const years = Math.floor(ms / YEARS_MS);

	if (years > 0) {
		const yearsRemainderMs = ms % YEARS_MS;
		const days = Math.floor(yearsRemainderMs / DAYS_MS);

		return `${pluralize("year", years, true)}, ${pluralize("day", days, true)}`;
	}

	const days = Math.floor(ms / DAYS_MS);
	if (days > 0) {
		const daysRemainderMs = ms % DAYS_MS;
		const hours = Math.floor(daysRemainderMs / HOURS_MS);

		return `${pluralize("day", days, true)}, ${pluralize("hour", hours, true)}`;
	}

	const hours = Math.floor(ms / HOURS_MS);
	if (hours > 0) {
		const hoursRemainderMs = ms % HOURS_MS;
		const minutes = Math.floor(hoursRemainderMs / MINUTES_MS);

		return `${pluralize("hour", hours, true)}, ${pluralize("minute", minutes, true)}`;
	}

	const minutes = Math.floor(ms / MINUTES_MS);
	if (minutes > 0) {
		const minutesRemainderMs = ms % MINUTES_MS;
		const seconds = Math.floor(minutesRemainderMs / SECONDS_MS);

		return `${pluralize("minute", minutes, true)}, ${pluralize("second", seconds, true)}`;
	}

	const seconds = Math.floor(ms / SECONDS_MS);
	if (seconds > 0) {
		return pluralize("second", seconds, true);
	}

	return "less than a second";
};

const humanizeMsShort = (ms) => {
	const hours = Math.floor(ms / HOURS_MS);
	if (hours > 0) {
		const hoursRemainderMs = ms % HOURS_MS;
		const minutes = Math.floor(hoursRemainderMs / MINUTES_MS);

		return `${hours}h ${minutes}m`;
	}

	const minutes = Math.floor(ms / MINUTES_MS);
	if (minutes > 0) {
		const minutesRemainderMs = ms % MINUTES_MS;
		const seconds = Math.floor(minutesRemainderMs / SECONDS_MS);

		return `${minutes}m ${seconds}s`;
	}

	const seconds = Math.floor(ms / SECONDS_MS);
	return `${seconds}s`;
};

const humanizeMin = (min) => {
	const ms = min * MINUTES_MS;
	const years = Math.floor(ms / YEARS_MS);

	if (years > 0) {
		const yearsRemainderMs = ms % YEARS_MS;
		const days = Math.floor(yearsRemainderMs / DAYS_MS);

		let result = pluralize("year", years, true);
		if (days > 0) result += ` ${pluralize("day", days, true)}`;
		return result;
	}

	const days = Math.floor(ms / DAYS_MS);
	if (days > 0) {
		const daysRemainderMs = ms % DAYS_MS;
		const hours = Math.floor(daysRemainderMs / HOURS_MS);

		let result = pluralize("day", days, true);
		if (hours > 0) result += ` ${pluralize("hour", hours, true)}`;
		return result;
	}

	const hours = Math.floor(ms / HOURS_MS);
	if (hours > 0) {
		const hoursRemainderMs = ms % HOURS_MS;
		const minutes = Math.floor(hoursRemainderMs / MINUTES_MS);

		let result = pluralize("hour", hours, true);
		if (minutes > 0) result += ` ${pluralize("minute", minutes, true)}`;
		return result;
	}

	const minutes = Math.floor(ms / MINUTES_MS);
	if (minutes > 0) {
		const minutesRemainderMs = ms % MINUTES_MS;
		const seconds = Math.floor(minutesRemainderMs / SECONDS_MS);

		let result = pluralize("minute", minutes, true);
		if (seconds > 0) result += ` ${pluralize("second", seconds, true)}`;
		return result;
	}

	const seconds = Math.floor(ms / SECONDS_MS);
	if (seconds > 0) return pluralize("second", seconds, true);

	return null;
};

const humanizeMinShort = (min) => {
	const ms = minToMsRaw(min);
	const result = humanizeMsShort(ms);
	return result;
};

const msToMinRaw = (ms) => {
	const minutes = Math.ceil(ms / MINUTES_MS) || 1;
	return minutes;
};

const msToSecRaw = (ms) => {
	const seconds = Math.ceil(ms / SECONDS_MS) || 1;
	return seconds;
};

const msToMin = (ms) => {
	const minutes = msToMinRaw(ms);
	return `${minutes}m`;
};

const minToMsRaw = (min) => {
	const ms = min * MINUTES_MS;
	return ms;
};

const minToSecRaw = (min) => {
	const sec = min * MINUTES_SEC;
	return sec;
};

module.exports = {
	YEARS_MS,
	DAYS_MS,
	HOURS_MS,
	MINUTES_MS,
	MINUTES_SEC,
	SECONDS_MS,
	humanizeMs,
	humanizeMsShort,
	humanizeMin,
	humanizeMinShort,
	minToMsRaw,
	minToSecRaw,
	msToMinRaw,
	msToMin,
	msToSecRaw,
};
