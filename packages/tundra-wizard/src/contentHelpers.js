const _ = require('lodash');

const memoizeOneModule = require('memoize-one');
const { reverse } = require('named-urls');

const routes = require('./routes');

const memoizeOne = memoizeOneModule.default || memoizeOneModule;

const recursivelyInflateContentInner = (input) => {
	const {
		content,
		contentParentUuid,
		// contentParent,
		skipParent,
	} = input;

	const contentByParent = _.groupBy(content, 'parentUuid');

	const contentAtLvl = contentByParent[contentParentUuid];
	const sortedContent = _.sortBy(contentAtLvl, 'position');

	const inflatedContent = sortedContent.map((contentInstance) => {
		// 20220325JP: For some reason these can't be spread out; need to enumerate.
		const {
			createdAt,
			markdown,
			name,
			parentUuid,
			position,
			productUuid,
			readingTimeMs,
			slug,
			summary,
			updatedAt,
			uuid,
		} = contentInstance;

		const toReturn = {
			...contentInstance,

			// 20220325JP: Explicitly return contentInstance attributes.
			createdAt,
			markdown,
			name,
			parentUuid,
			position,
			productUuid,
			readingTimeMs,
			slug,
			summary,
			updatedAt,
			uuid,
		};

		// if (!skipParent) {
		//   toReturn.parent = contentParent;
		// }

		const inflatedChildContent = recursivelyInflateContentInner({
			content,
			contentParentUuid: contentInstance.uuid,
			// contentParent: toReturn,
			skipParent,
		});

		toReturn.content = inflatedChildContent;

		return toReturn;
	});

	return inflatedContent;
};

function recursivelyAnnotateInflatedContent({ content, depth = 0, parentTitlePrefix = '', product }) {
	const results = [];

	const isRootLevel = depth === 0;
	const firstNodeChildContent = _.get(content, '[0].content');
	const shouldSkipFirstNode = isRootLevel && firstNodeChildContent && firstNodeChildContent.length === 0;
	const numberingOffset = shouldSkipFirstNode ? 1 : 0;

	for (let i = 0; i < content.length; i += 1) {
		const node = content[i];

		const hasContent = node.content && node.content.length > 0;
		const isIntroduction = i === 0 && !hasContent;
		const isWrappingUp = i === content.length - 1 && !hasContent;
		const shouldSkipNumbering = isRootLevel && (isIntroduction || isWrappingUp);
		const isAchievableProduct = product.slug === 'achievable';

		const titlePrefix = (() => {
			if (isAchievableProduct || shouldSkipNumbering) return '';

			const level = i + 1 - numberingOffset;

			if (parentTitlePrefix) {
				const parentTitlePrefixLevels = _.compact(parentTitlePrefix.split('.'));
				const result = [...parentTitlePrefixLevels, level].join('.');
				return result;
			}

			return `${level}.`;
		})();

		const {
			createdAt,
			markdown,
			name,
			parentUuid,
			position,
			productUuid,
			readingTimeMs,
			slug,
			summary,
			updatedAt,
			uuid,
		} = node;
		const { slug: productSlug } = product;
		const key = `content-${uuid}`;
		const title = !shouldSkipNumbering && titlePrefix ? `${titlePrefix} ${name}` : name;
		const path = reverse(routes.study.learn.content, {
			productSlug,
			contentUuid: slug,
		});

		const annotatedNode = {
			...node,
			createdAt,
			markdown,
			name,
			parentUuid,
			position,
			productUuid,
			readingTimeMs,
			slug,
			summary,
			updatedAt,
			uuid,
			key,
			path,
			titlePrefix,
			title,
		};

		if (hasContent) {
			annotatedNode.content = recursivelyAnnotateInflatedContent({
				content: node.content,
				depth: depth + 1,
				parentTitlePrefix: titlePrefix,
				product,
			});
		}

		results.push(annotatedNode);
	}

	return results;
}

const recursivelyInflateContent = (input) => {
	const { product } = input;

	const inflatedContent = recursivelyInflateContentInner(input);

	const annotatedInflatedContent = recursivelyAnnotateInflatedContent({ content: inflatedContent, product }); //?

	return annotatedInflatedContent;
};

const recursivelyFlattenContent = ({ inflatedContent }) => {
	if (_.isEmpty(inflatedContent)) {
		return [];
	}

	const lvlFlattenedContent = inflatedContent.reduce((acc, obj) => {
		acc.push(obj);
		acc.push(
			...recursivelyFlattenContent({
				inflatedContent: obj.content,
			})
		);
		return acc;
	}, []);

	return lvlFlattenedContent;
};

const inflateContent = ({ product, content }) => {
	const inflatedContent = recursivelyInflateContent({
		product,
		content,
		contentTitlePrefix: null,
		contentParentUuid: null,
	});

	const flatContent = recursivelyFlattenContent({
		inflatedContent,
	});

	return {
		flatContent,
		inflatedContent,
	};
};

const recursiveInfo = (node) => {
	const ourInfo = {
		uuids: [node.uuid],
		itemsReady: node.itemsReady,
		itemsAvailable: node.itemsAvailable,
	};

	if (_.isEmpty(node.content)) {
		return ourInfo;
	}

	const childrenInfo = _.flatten(
		node.content.map((child) => {
			return recursiveInfo(child);
		})
	);

	const result = childrenInfo.reduce((acc, child) => {
		acc.uuids = acc.uuids.concat(child.uuids);
		acc.itemsReady += child.itemsReady;
		acc.itemsAvailable += child.itemsAvailable;
		return acc;
	}, ourInfo);

	return result;
};

const countItemsAvailable = memoizeOne(({ contentUuids, content }) => {
	const contentByUuid = _.keyBy(content, 'uuid');
	const count = contentUuids.reduce((acc, contentUuid) => {
		const thisCount = _.get(contentByUuid, [contentUuid, 'itemsAvailable'], 0);
		return acc + thisCount;
	}, 0);
	return count;
});

const firstContentPath = ({ content }) => {
	if (!content.content || content.content.length === 0) {
		return content.path;
	}

	return firstContentPath({ content: content.content[0] });
};

const fullName = ({ content, contentByUuid }) => {
	const { name, parentUuid } = content;

	if (!content.parentUuid) {
		return name;
	}

	const parent = contentByUuid[parentUuid];
	if (!parent) {
		return name;
	}

	return `${name} | ${fullName({ content: parent, contentByUuid })}`;
};

module.exports = {
	inflateContent,
	recursivelyFlattenContent,
	recursivelyInflateContent,
	recursiveInfo,
	countItemsAvailable,
	firstContentPath,
	fullName,
};
