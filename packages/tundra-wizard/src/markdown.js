const _ = require("lodash");
const cheerio = require("cheerio");
const md = require("./markdown/index");

const MIN_MARKDOWN_LENGTH = 20;

const getHasMarkdown = ({ markdown }) => {
	const isLessThanMinLength = (markdown || "").length < MIN_MARKDOWN_LENGTH;
	const isEmpty = !markdown || _.isEmpty(markdown) || isLessThanMinLength;
	return !isEmpty;
};

const renderMarkdown = (markdown, options = {}) => {
	const { isRendered } = options;

	const rendered = isRendered ? markdown : md.render(markdown.toString());
	const markdownText = cheerio.load(rendered).text().trim();
	const markdownTextSingleLine = markdownText.replace(/[\r\n]+/g, " ");
	const markdownTextSnippet = _.truncate(markdownTextSingleLine, { length: 150 });

	return {
		rendered,
		markdownText,
		markdownTextSingleLine,
		markdownTextSnippet,
	};
};

module.exports = {
	getHasMarkdown,
	md,
	renderMarkdown,
};
