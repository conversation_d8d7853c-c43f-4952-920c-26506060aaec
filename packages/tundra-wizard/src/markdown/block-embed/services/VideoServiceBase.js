"use strict";

const TITLE_REGEX = new RegExp("&quot;(.*)&quot;");

function defaultUrlFilter(url, _videoID, _serviceName, _options) {
	return url;
}

class VideoServiceBase {
	constructor(name, options, env) {
		this.name = name;
		this.options = Object.assign(this.getDefaultOptions(), options);
		this.env = env;
	}

	getDefaultOptions() {
		return {};
	}

	extractVideoID(reference) {
		return reference;
	}

	getVideoUrl(_videoID) {
		throw new Error("not implemented");
	}

	getFilteredVideoUrl(videoID) {
		let filterUrlDelegate =
			typeof this.env.options.filterUrl === "function" ? this.env.options.filterUrl : defaultUrlFilter;
		let videoUrl = this.getVideoUrl(videoID);
		return filterUrlDelegate(videoUrl, this.name, videoID, this.env.options);
	}

	getVideoTile(videoUrl) {
		const match = videoUrl.match(TITLE_REGEX);

		if (!match) return null;

		const [_, title] = match;
		return title;
	}

	getEmbedCode(videoID) {
		let containerClassNames = [];
		if (this.env.options.containerClassName) {
			containerClassNames.push(this.env.options.containerClassName);
		}

		let escapedServiceName = this.env.md.utils.escapeHtml(this.name);
		containerClassNames.push(this.env.options.serviceClassPrefix + escapedServiceName);

		const videoUrl = this.getFilteredVideoUrl(videoID);
		const src = videoUrl.split(" ")[0];
		const title = this.getVideoTile(videoUrl);

		let iframeAttributeList = [];
		iframeAttributeList.push(["type", "text/html"]);
		iframeAttributeList.push(["src", src]);
		iframeAttributeList.push(["frameborder", 0]);
		if (title) iframeAttributeList.push(["title", title]);

		if (this.env.options.outputPlayerSize === true) {
			if (this.options.width !== undefined && this.options.width !== null) {
				iframeAttributeList.push(["width", this.options.width]);
			}
			if (this.options.height !== undefined && this.options.height !== null) {
				iframeAttributeList.push(["height", this.options.height]);
			}
		}

		if (this.env.options.allowFullScreen === true) {
			iframeAttributeList.push(["webkitallowfullscreen"]);
			iframeAttributeList.push(["mozallowfullscreen"]);
			iframeAttributeList.push(["allowfullscreen"]);
		}

		let iframeAttributes = iframeAttributeList
			.map((pair) => (pair[1] !== undefined ? `${pair[0]}="${pair[1]}"` : pair[0]))
			.join(" ");

		return `<div class="${containerClassNames.join(" ")}">` + `<iframe ${iframeAttributes}></iframe>` + `</div>\n`;
	}
}

module.exports = VideoServiceBase;
