const md = require("../index");

describe("html", () => {
	test("default", async () => {
		const result = md.render("<b>bold</b>");
		expect(result).toEqual("<b>bold</b>\n");
	});
});

describe("class", () => {
	test("default", async () => {
		const result = md.render("::: class center\n\n**strong**");
		expect(result).toEqual('<div class="wisdomClass center">\n<p><strong>strong</strong></p>\n</div>\n');
	});
});

describe("deflist", () => {
	test("default", async () => {
		const result = md.render("::: definitions\n\nTerm 1\n: Definition 1\n:::");
		expect(result).toEqual(
			'<fieldset class="wisdomDefinitions"><legend>Definitions</legend>\n<dl>\n<dt>Term 1</dt>\n<dd>Definition 1</dd>\n</dl>\n</fieldset>\n'
		);
	});
});

describe("points", () => {
	test("default", async () => {
		const result = md.render("::: points\nMore text here\n\n:::");
		expect(result).toEqual(
			'<fieldset class="wisdomKeyPoints"><legend>Key points</legend>\n<p>More text here</p>\n</fieldset>\n'
		);
	});
});

describe("sidenote", () => {
	test("default", async () => {
		const result = md.render("::: sidenote Registration with the SEC\nMore text here\n\n:::");
		expect(result).toEqual(
			'<fieldset class="wisdomSidenote"><legend>Sidenote</legend>\n<div class="wisdomSidenoteTitle">Registration with the SEC</div>\n<p>More text here</p>\n</fieldset>\n'
		);
	});

	test("with center", async () => {
		const result = md.render(
			":::::: sidenote Registration with the SEC\n\none\n::: class center\ncenter\n:::\nMore text here\n\n::::::"
		);
		expect(result)
			.toEqual(`<fieldset class="wisdomSidenote"><legend>Sidenote</legend>\n<div class="wisdomSidenoteTitle">Registration with the SEC</div>
<p>one</p>
<div class="wisdomClass center">
<p>center</p>
</div>
<p>More text here</p>\n</fieldset>\n`);
	});
});

describe("spoiler", () => {
	test("default", async () => {
		const result = md.render("::: spoiler\nMore text here\n\n:::");
		expect(result).toEqual(
			'<div class="spoiler">\n<div class="spoilerInfoContainer"><div class="spoilerInfo">(spoiler)</div></div>\n<p>More text here</p>\n</div>\n'
		);
	});
});

describe("block", () => {
	test("default", async () => {
		const result = md.render("::: block\nMore text here\n\n:::");
		expect(result).toEqual('<fieldset class="wisdomBlock">\n<p>More text here</p>\n</fieldset>\n');
	});
});

describe("callout", () => {
	test("default", async () => {
		const result = md.render("::: callout\nMore text here\n\n:::");
		expect(result).toEqual('<fieldset class="wisdomCallout">\n<p>More text here</p>\n</fieldset>\n');
	});
});

describe("edge cases", () => {
	test("renders string 0", async () => {
		const result = md.render("0");
		expect(result).toEqual("<p>0</p>\n");
	});
});
