const md = require("../index");

describe("block-embed", () => {
	test("works with a title", async () => {
		const result = md.render('@[vimeo](487599024?h=82035287bc "Common stock basic characteristics")');
		expect(result).toEqual(
			'<div class="block-embed block-embed-service-vimeo"><iframe type="text/html" src="//player.vimeo.com/video/487599024?h=82035287bc" frameborder="0" title="Common stock basic characteristics" width="500" height="281" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe></div>\n'
		);
	});

	test("works without a title", async () => {
		const result = md.render("@[vimeo](487599024?h=82035287bc)");
		expect(result).toEqual(
			'<div class="block-embed block-embed-service-vimeo"><iframe type="text/html" src="//player.vimeo.com/video/487599024?h=82035287bc" frameborder="0" width="500" height="281" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe></div>\n'
		);
	});
});
