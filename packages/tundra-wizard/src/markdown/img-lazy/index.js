const lazy_loading_plugin = (md, mdOptions) => {
	const defaultImageRenderer = md.renderer.rules.image;

	md.renderer.rules.image = function (tokens, idx, options, env, self) {
		const token = tokens[idx];
		token.attrSet("loading", "lazy");

		if (mdOptions && mdOptions.decoding === true) {
			token.attrSet("decoding", "async");
		}

		return defaultImageRenderer(tokens, idx, options, env, self);
	};
};

module.exports = lazy_loading_plugin;
