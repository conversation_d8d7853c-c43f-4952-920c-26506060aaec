const MarkdownIt = require("markdown-it");
const markdownItContainer = require("markdown-it-container");
const markdownItDeflist = require("markdown-it-deflist");
const markdownItLinkAttributes = require("markdown-it-link-attributes");
const markdownItFontAwesome = require("markdown-it-fontawesome");
const markdownItJsx = require("markdown-it-jsx");
const markdownItKatex = require("./katex");
const markdownItImgLazy = require("./img-lazy");
const markdownItBlockEmbed = require("./block-embed");

const md = new MarkdownIt({
	linkify: true,
	typographer: true,
	xhtmlOut: true,
	html: true,
});

md.use(markdownItKatex, {
	output: "html",
});
md.use(markdownItBlockEmbed, {
	allowFullScreen: true,
});
md.use(markdownItFontAwesome);
md.use(markdownItDeflist);
md.use(markdownItLinkAttributes, [
	{
		matcher(href) {
			return /^https?:\/\//.test(href);
		},
		attrs: {
			target: "_blank",
			rel: "noopener noreferrer",
			"aria-label": "External link (opens in new window)",
		},
	},
]);
md.use(markdownItContainer, "block", {
	validate: (params) => {
		return params.trim().match(/^block\s*$/);
	},
	render: (tokens, idx) => {
		if (tokens[idx].nesting === 1) {
			return '<fieldset class="wisdomBlock">\n';
		}

		return "</fieldset>\n";
	},
});
md.use(markdownItContainer, "callout", {
	validate: (params) => {
		return params.trim().match(/^callout\s*$/);
	},
	render: (tokens, idx) => {
		if (tokens[idx].nesting === 1) {
			return '<fieldset class="wisdomCallout">\n';
		}

		return "</fieldset>\n";
	},
});
md.use(markdownItContainer, "definitions", {
	validate: (params) => {
		return params.trim().match(/^definitions\s*$/);
	},
	render: (tokens, idx) => {
		if (tokens[idx].nesting === 1) {
			return '<fieldset class="wisdomDefinitions"><legend>Definitions</legend>\n';
		}

		return "</fieldset>\n";
	},
});
md.use(markdownItContainer, "points", {
	validate: (params) => {
		return params.trim().match(/^points\s*$/);
	},
	render: (tokens, idx) => {
		if (tokens[idx].nesting === 1) {
			return '<fieldset class="wisdomKeyPoints"><legend>Key points</legend>\n';
		}

		return "</fieldset>\n";
	},
});
md.use(markdownItContainer, "sidenote", {
	validate: (params) => {
		return params.trim().match(/^sidenote\s+(.*)$/);
	},
	render: (tokens, idx) => {
		const matches = tokens[idx].info.trim().match(/^sidenote\s+(.*)$/);

		if (tokens[idx].nesting === 1) {
			return `<fieldset class="wisdomSidenote"><legend>Sidenote</legend>\n<div class="wisdomSidenoteTitle">${matches[1]}</div>\n`;
		}

		return "</fieldset>\n";
	},
});
md.use(markdownItContainer, "spoiler", {
	validate: (params) => {
		return params.trim().match(/^spoiler\s*$/);
	},
	render: (tokens, idx) => {
		if (tokens[idx].nesting === 1) {
			return '<div class="spoiler">\n<div class="spoilerInfoContainer"><div class="spoilerInfo">(spoiler)</div></div>\n';
		}

		return "</div>\n";
	},
});
md.use(markdownItContainer, "class", {
	validate: (params) => {
		return params.trim().match(/^class\s+(.*)$/);
	},
	render: (tokens, idx) => {
		const matches = tokens[idx].info.trim().match(/^class\s+(.*)$/);

		if (tokens[idx].nesting === 1) {
			return `<div class="wisdomClass ${matches[1]}">\n`;
		}

		return "</div>\n";
	},
});
md.use(markdownItJsx);
md.use(markdownItImgLazy);

module.exports = md;
