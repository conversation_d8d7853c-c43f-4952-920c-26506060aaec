const _ = require('lodash');

const FINRA_SERIES_6_EXAMS = [
	{
		variant: 'common-stock',
		rootContentUuid: '6643c39f-c589-43ce-814a-ba5fb059841b',
		nextContentUuid: '83ca1ded-cd68-4f61-bb41-22b753da8cb6',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 1',
		title: 'Common stock',
	},
	{
		variant: 'preferred-stock',
		rootContentUuid: '22a20844-1de4-47a8-877a-987a94ec632e',
		nextContentUuid: 'faa36cc5-1231-4352-a8c7-62044a0f3c80',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 2',
		title: 'Preferred stock',
	},
	{
		variant: 'debt-securities',
		rootContentUuid: '90dd41f6-5fe0-48c2-9c5b-10dd174968ce',
		nextContentUuid: '8632d3e7-97eb-4472-84dd-21a9fe2271c2',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 3',
		title: 'Debt securities',
	},
	{
		variant: 'chapters-2-to-4',
		rootContentUuid: null,
		nextContentUuid: '8632d3e7-97eb-4472-84dd-21a9fe2271c2',
		numQuestions: 25,
		allottedTimeMins: 40,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapters 1 to 3',
	},
	{
		variant: 'corporate-debt',
		rootContentUuid: '19fc26ed-bed6-4b96-834a-bbf6ecdcce7d',
		nextContentUuid: '0f906490-c2be-47f7-9077-57bae15ac5c5',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 4',
		title: 'Corporate debt',
	},
	{
		variant: 'municipal-debt',
		rootContentUuid: '4f8e7b35-47b0-4b7c-be80-0c67fc86ba5f',
		nextContentUuid: '1d452530-554a-430e-8635-9e340232e9bb',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 5',
		title: 'Municipal debt',
	},
	{
		variant: 'us-government-debt',
		rootContentUuid: '6013d3aa-9592-472a-9b96-a1fb192f8d5d',
		nextContentUuid: '17ea8e1d-514a-47a0-b1c8-f90e3a2cc4ee',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 6',
		title: 'US government debt',
	},
	{
		variant: 'chapters-5-to-7',
		rootContentUuid: null,
		nextContentUuid: '17ea8e1d-514a-47a0-b1c8-f90e3a2cc4ee',
		numQuestions: 25,
		allottedTimeMins: 40,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapters 4 to 6',
	},
	{
		variant: 'investment-companies',
		rootContentUuid: '913c12d8-adf3-4e5e-9141-99e8e51b6156',
		nextContentUuid: '51019b2e-09e6-4d57-9c11-d879ac80d4f7',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 7',
		title: 'Investment companies',
	},
	{
		variant: 'chapters-2-to-8',
		rootContentUuid: null,
		nextContentUuid: '51019b2e-09e6-4d57-9c11-d879ac80d4f7',
		numQuestions: 30,
		allottedTimeMins: 45,
		type: 'midterm',
		name: 'Midterm',
		title: 'Chapters 1 to 7',
	},
	{
		variant: 'insurance-products',
		rootContentUuid: 'e32f146f-a32a-4633-955a-47afa28266fc',
		nextContentUuid: '935e6378-ef49-4b9d-b2bd-ecf8cad94c9e',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 8',
		title: 'Insurance products',
	},
	{
		variant: 'the-primary-market',
		rootContentUuid: 'bdf7b12a-2d9d-466d-b34d-32173976dd88',
		nextContentUuid: 'b51ceee1-b4e5-437b-8763-e47ca1951142',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 9',
		title: 'The primary market',
	},
	{
		variant: 'the-secondary-market',
		rootContentUuid: '786aeef9-1560-466a-8599-d1c1c1fbf228',
		nextContentUuid: '10d63012-d8c1-497a-bed5-ac5805b5792d',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 10',
		title: 'The secondary market',
	},
	{
		variant: 'chapters-8-to-11',
		rootContentUuid: null,
		nextContentUuid: '10d63012-d8c1-497a-bed5-ac5805b5792d',
		numQuestions: 25,
		allottedTimeMins: 40,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapters 7 to 10',
	},
	{
		variant: 'brokerage-accounts',
		rootContentUuid: '36faffc4-5a76-4eb5-8a1c-7aa7d7c70dcb',
		nextContentUuid: 'af627639-f6c0-4f57-8d2a-aa3ec839c5ed',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 11',
		title: 'Brokerage accounts',
	},
	{
		variant: 'retirement-and-education-plans',
		rootContentUuid: '2e010d00-e732-4b29-a9cc-71d251228a4b',
		nextContentUuid: '718e7c27-d526-4624-8282-c6a9d8065bd6',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 12',
		title: 'Retirement and education plans',
	},
	{
		variant: 'rules-and-ethics',
		rootContentUuid: 'b77cf6f7-beab-4e2a-98aa-e84810984b4e',
		nextContentUuid: '7d76dae2-c74a-42a6-85c9-22b288fa7eb0',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 13',
		title: 'Rules and ethics',
	},
	{
		variant: 'suitability',
		rootContentUuid: '58e6955b-d862-4a55-aec2-6edeb25ed078',
		nextContentUuid: 'f109a385-ba69-400f-b400-0141f6705bd2',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 14',
		title: 'Suitability',
	},
	{
		variant: 'chapters-12-to-15',
		rootContentUuid: null,
		nextContentUuid: 'f109a385-ba69-400f-b400-0141f6705bd2',
		numQuestions: 25,
		allottedTimeMins: 40,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapters 11 to 14',
	},
	{
		variant: 'final-1',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 50,
		allottedTimeMins: 90,
		type: 'final',
		name: 'Final exam A',
	},
	{
		variant: 'final-2',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 50,
		allottedTimeMins: 90,
		type: 'final',
		name: 'Final exam B',
	},
	{
		variant: 'exam',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 50,
		allottedTimeMins: 90,
		type: 'exam',
		name: 'Series 6 full exam',
	},
];

const FINRA_SERIES_6_EXAMS_BY_VARIANT = _.keyBy(FINRA_SERIES_6_EXAMS, 'variant');
FINRA_SERIES_6_EXAMS_BY_VARIANT['retirement-education-plans'] =
	FINRA_SERIES_6_EXAMS_BY_VARIANT['retirement-and-education-plans'];
FINRA_SERIES_6_EXAMS_BY_VARIANT['rules-ethics'] = FINRA_SERIES_6_EXAMS_BY_VARIANT['rules-and-ethics'];

module.exports = { FINRA_SERIES_6_EXAMS, FINRA_SERIES_6_EXAMS_BY_VARIANT };
