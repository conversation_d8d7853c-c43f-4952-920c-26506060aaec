const _ = require('lodash');

const FINRA_SERIES_7_EXAMS = [
	{
		variant: 'common-stock',
		rootContentUuid: '30c46146-a37a-42fa-8f15-b407c5485560',
		nextContentUuid: '1f8b08c0-23d8-454c-af53-17a5032222b4',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 1',
		title: 'Common stock',
	},
	{
		variant: 'preferred-stock',
		rootContentUuid: '8b7e9896-abd4-40da-8257-8427b05dec1c',
		nextContentUuid: 'ff996a22-a2dc-42af-b65b-b27448c831e1',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 2',
		title: 'Preferred stock',
	},
	{
		variant: 'bond-fundamentals',
		rootContentUuid: '787bc02f-fadd-46d1-9f7b-323a144a1b36',
		nextContentUuid: '1a6a4b46-5ffc-4f92-af98-53efac06381d',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 3',
		title: 'Bond fundamentals',
	},
	{
		variant: 'corporate-debt',
		rootContentUuid: '3f402496-35a2-46eb-8008-bbd36584e26a',
		nextContentUuid: '248589a9-5b97-4598-ada5-a3618a9d41e2',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 4',
		title: 'Corporate debt',
	},
	{
		variant: 'chapters-1-to-4',
		rootContentUuid: null,
		nextContentUuid: '248589a9-5b97-4598-ada5-a3618a9d41e2',
		numQuestions: 60,
		allottedTimeMins: 90,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapters 1 to 4',
	},
	{
		variant: 'municipal-debt',
		rootContentUuid: 'eb686e0d-e598-48a7-bb34-6b6efca54339',
		nextContentUuid: 'd65369af-8e97-4a46-8e93-844963d809d2',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 5',
		title: 'Municipal debt',
	},
	{
		variant: 'us-government-debt',
		rootContentUuid: '570397ed-fa04-4fd6-93aa-2afd15960c57',
		nextContentUuid: '61dd2881-34a0-4e36-ba6b-eeba08f183ad',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 6',
		title: 'US government debt',
	},
	{
		variant: 'investment-companies',
		rootContentUuid: 'cbc15cf8-23ba-49ff-b0a4-165e92ef0ff1',
		nextContentUuid: '80476b15-5a1c-4636-ad32-f81adcced5f1',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 7',
		title: 'Investment companies',
	},
	{
		variant: 'alternative-pooled-investments',
		rootContentUuid: '7c787b8e-6341-4b30-9088-1bf7adf138a9',
		nextContentUuid: '0224dd25-a655-44cc-9a57-722c599d9535',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 8',
		title: 'Alternative pooled investments',
	},
	{
		variant: 'chapters-5-to-8',
		rootContentUuid: null,
		nextContentUuid: '0224dd25-a655-44cc-9a57-722c599d9535',
		numQuestions: 60,
		allottedTimeMins: 90,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapters 5 to 8',
	},
	{
		variant: 'chapters-1-to-8',
		rootContentUuid: null,
		nextContentUuid: '0224dd25-a655-44cc-9a57-722c599d9535',
		numQuestions: 60,
		allottedTimeMins: 90,
		type: 'midterm',
		name: 'Midterm',
		title: 'Chapters 1 to 8',
	},
	{
		variant: 'options',
		rootContentUuid: 'd173508f-8ab6-4c5f-b494-f57057dafb46',
		nextContentUuid: '4d27cc57-b877-4eca-a69b-91f7248b4d09',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 9',
		title: 'Options',
	},
	{
		variant: 'taxes',
		rootContentUuid: 'ac0c3df0-0327-4197-bf69-a84e6692eed8',
		nextContentUuid: '73ede6fc-3dfe-4ac9-870d-bd558faedce5',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 10',
		title: 'Taxes',
	},
	{
		variant: 'the-primary-market',
		rootContentUuid: '999e1000-d435-4307-84f3-70469eef101d',
		nextContentUuid: '6d487023-e9fc-4d37-b4fa-6b5708977ce1',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 11',
		title: 'The primary market',
	},
	{
		variant: 'the-secondary-market',
		rootContentUuid: 'a8c424fc-d259-496a-bfc4-0613cfdb0434',
		nextContentUuid: '1dddaf49-67d9-4172-aac3-f1f142371679',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 12',
		title: 'The secondary market',
	},
	{
		variant: 'chapters-9-to-12',
		rootContentUuid: null,
		nextContentUuid: '1dddaf49-67d9-4172-aac3-f1f142371679',
		numQuestions: 60,
		allottedTimeMins: 90,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapters 9 to 12',
	},
	{
		variant: 'brokerage-accounts',
		rootContentUuid: 'a44e9256-7813-499e-b847-32d2aa7d8516',
		nextContentUuid: 'b72f6d9c-4686-4a35-844a-bdba1be92531',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 13',
		title: 'Brokerage accounts',
	},
	{
		variant: 'retirement-and-education-plans',
		rootContentUuid: '8bd084b1-6c2b-40cd-af94-137064f8631a',
		nextContentUuid: 'f0f6c4c3-85f5-4be0-8732-9b6ac18d34c0',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 14',
		title: 'Retirement and education plans',
	},
	{
		variant: 'rules-and-ethics',
		rootContentUuid: '8d9bcdbb-ece2-4585-b8c1-e510a5dc99d5',
		nextContentUuid: '1a203186-a86e-464a-a692-3339e3ae5da7',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 15',
		title: 'Rules and ethics',
	},
	{
		variant: 'suitability',
		rootContentUuid: '6e6b3ed2-96f6-4cb3-88c4-927f6f7635a5',
		nextContentUuid: '1028f864-3146-4389-a3e7-1a669de9b8e0',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 16',
		title: 'Suitability',
	},
	{
		variant: 'chapters-13-to-16',
		rootContentUuid: null,
		nextContentUuid: '1028f864-3146-4389-a3e7-1a669de9b8e0',
		numQuestions: 60,
		allottedTimeMins: 90,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapters 13 to 16',
	},
	{
		variant: 'final-1',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 125,
		allottedTimeMins: 225,
		type: 'final',
		name: 'Final exam A',
	},
	{
		variant: 'final-2',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 125,
		allottedTimeMins: 225,
		type: 'final',
		name: 'Final exam B',
	},
	{
		variant: 'exam',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 125,
		allottedTimeMins: 225,
		type: 'exam',
		name: 'Series 7 full exam',
	},
];

const FINRA_SERIES_7_EXAMS_BY_VARIANT = _.keyBy(FINRA_SERIES_7_EXAMS, 'variant');
FINRA_SERIES_7_EXAMS_BY_VARIANT['primary-market'] = FINRA_SERIES_7_EXAMS_BY_VARIANT['the-primary-market'];
FINRA_SERIES_7_EXAMS_BY_VARIANT['secondary-market'] = FINRA_SERIES_7_EXAMS_BY_VARIANT['the-secondary-market'];

module.exports = { FINRA_SERIES_7_EXAMS, FINRA_SERIES_7_EXAMS_BY_VARIANT };
