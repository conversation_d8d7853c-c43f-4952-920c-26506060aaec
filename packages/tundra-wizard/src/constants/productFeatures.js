const numeral = require("numeral");
const COURSE_STATS = require("./courseStats");

const greStats = COURSE_STATS["gre-v2"];

const ONLINE_TEXTBOOK = "Easy-to-understand online textbook";
const AUDIO = "Narrated textbook audio";
const AI_TUTOR = "Ask Achievable AI tutor chat";
const STUDY_PLANNER = "Adaptive study planner";

const formatChapterQuizzes = ({ num }) =>
	num < 1000 ? numeral(num).format("0a") : numeral(num).format(num % 1000 === 0 ? "0a" : "0.0a");

const getChapterQuizzes = ({ stats }) => `${formatChapterQuizzes({ num: stats.numChapterQuizzes })}+ chapter quizzes`;
const getPracticeExams = ({ stats }) => `${stats.numPracticeExams}+ practice exams`;
const getBonusVideos = ({ stats }) => `${stats.numVideos}+ bonus videos`;

const getStandardStats = ({ productSlug }) => {
	const stats = COURSE_STATS[productSlug];

	const features = [ONLINE_TEXTBOOK];

	if (stats.numChapterQuizzes) {
		features.push(getChapterQuizzes({ stats }));
	}

	if (stats.numPracticeExams) {
		features.push(getPracticeExams({ stats }));
	}

	if (stats.numVideos) {
		features.push(getBonusVideos({ stats }));
	}

	if (stats.hasAudio) {
		features.push(AUDIO);
	}

	features.push(AI_TUTOR);
	features.push(STUDY_PLANNER);

	return features;
};

const PRODUCT_FEATURES = {
	default: [ONLINE_TEXTBOOK, "Chapter quizzes", "Practice exams", STUDY_PLANNER],
	act: getStandardStats({ productSlug: "act" }),
	"amc-12": ["Online reference textbook", "250+ drill question templates", "Infinite randomized drills", AI_TUTOR],
	clt: getStandardStats({ productSlug: "clt" }),
	"finra-sie": getStandardStats({ productSlug: "finra-sie" }),
	"finra-series-6": getStandardStats({ productSlug: "finra-series-6" }),
	"finra-series-7": getStandardStats({ productSlug: "finra-series-7" }),
	"finra-series-9": getStandardStats({ productSlug: "finra-series-9" }),
	"finra-series-63": getStandardStats({ productSlug: "finra-series-63" }),
	"finra-series-65": getStandardStats({ productSlug: "finra-series-65" }),
	"finra-series-66": getStandardStats({ productSlug: "finra-series-66" }),
	"gre-v2": [
		ONLINE_TEXTBOOK,
		"Infinite randomized questions",
		`${greStats.numQuant}+ quant templates`,
		`${greStats.numPracticeExams} verbal sections`,
		"250 vocab words",
		"Unlimited essay grading",
		AI_TUTOR,
		STUDY_PLANNER,
	],
	"insurance-life-health": getStandardStats({ productSlug: "insurance-life-health" }),
	"insurance-life": getStandardStats({ productSlug: "insurance-life" }),
	"insurance-health": getStandardStats({ productSlug: "insurance-health" }),
	"usmle-step-1": getStandardStats({ productSlug: "usmle-step-1" }),
};

module.exports = PRODUCT_FEATURES;
