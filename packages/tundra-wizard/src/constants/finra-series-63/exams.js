const FINRA_SERIES_63_EXAMS = [
	{
		variant: 'chapter-2',
		rootContentUuid: 'f1bf5b92-3883-41c6-8dcc-56be9ced3e02',
		nextContentUuid: 'e634dd05-b602-4a13-a544-a2ac8c118c53',
		numQuestions: 30,
		allottedTimeMins: 45,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapter 1',
	},
	{
		variant: 'chapter-3',
		rootContentUuid: '7508ee9d-df19-45cd-aaaa-d010cc9111db',
		nextContentUuid: '4400deb6-697a-4b1d-9133-a3209e55864e',
		numQuestions: 30,
		allottedTimeMins: 45,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapter 2',
	},
	{
		variant: 'chapters-2-to-3',
		rootContentUuid: null,
		nextContentUuid: '4400deb6-697a-4b1d-9133-a3209e55864e',
		numQuestions: 30,
		allottedTimeMins: 45,
		type: 'midterm',
		name: 'Midterm',
		title: 'Chapters 1 to 2',
	},
	{
		variant: 'chapter-4',
		rootContentUuid: 'be5a0e84-4d16-4085-b9c6-3b075d6918aa',
		nextContentUuid: '6e5fbbd6-9774-42f9-85de-b56149dc5d6f',
		numQuestions: 30,
		allottedTimeMins: 45,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapter 3',
	},
	{
		variant: 'chapter-5',
		rootContentUuid: '161e02ec-4910-45ae-bf47-4b4cbdf101e4',
		nextContentUuid: '99851ab9-cdc7-408a-92a2-2c398ee8f1db',
		numQuestions: 30,
		allottedTimeMins: 45,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapter 4',
	},
	{
		variant: 'final-1',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 60,
		allottedTimeMins: 75,
		type: 'final',
		name: 'Final exam A',
	},
	{
		variant: 'final-2',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 60,
		allottedTimeMins: 75,
		type: 'final',
		name: 'Final exam B',
	},
	{
		variant: 'exam',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 60,
		allottedTimeMins: 75,
		type: 'exam',
		name: 'Series 63 full exam',
	},
];

module.exports = FINRA_SERIES_63_EXAMS;
