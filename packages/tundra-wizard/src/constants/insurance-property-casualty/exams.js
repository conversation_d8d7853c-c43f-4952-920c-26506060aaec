const _ = require("lodash");
const { US_STATES_BY_VARIANT } = require("../usStates");

const INSURANCE_PROPERTY_CASUALTY_EXAMS = [
	{
		variant: "exam",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "P&C full exam",
	},
	..._.map(US_STATES_BY_VARIANT, (state, variant) => ({
		variant,
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: `${state} P&C exam`,
	})),
];

module.exports = { INSURANCE_PROPERTY_CASUALTY_EXAMS };
