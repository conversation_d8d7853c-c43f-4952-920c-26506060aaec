const FINRA_SERIES_65_EXAMS = [
	{
		variant: 'chapter-2',
		rootContentUuid: '686b47cc-72e5-4613-9421-66c0d6b4a90a',
		nextContentUuid: '78d861e6-ac4f-4566-ab85-9a7779920ac4',
		numQuestions: 65,
		allottedTimeMins: 100,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapter 1',
	},
	{
		variant: 'chapter-3',
		rootContentUuid: '693a01e8-e08c-4610-8a46-897cb3921c4f',
		nextContentUuid: '13a01080-765f-4468-83b8-ac4e330620d1',
		numQuestions: 65,
		allottedTimeMins: 100,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapter 2',
	},
	{
		variant: 'chapters-2-to-3',
		rootContentUuid: null,
		nextContentUuid: '13a01080-765f-4468-83b8-ac4e330620d1',
		numQuestions: 65,
		allottedTimeMins: 100,
		type: 'midterm',
		name: 'Midterm',
		title: 'Chapters 1 to 2',
	},
	{
		variant: 'chapter-4',
		rootContentUuid: '8f8470b9-7cdf-4bc2-8f81-e2b2c7b8af56',
		nextContentUuid: '75fcacd9-45cc-4e34-94dd-7fbc71bbd115',
		numQuestions: 65,
		allottedTimeMins: 100,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapter 3',
	},
	{
		variant: 'chapter-5',
		rootContentUuid: 'db9c5a49-aa4f-4f32-9a10-da64c8d0bdfb',
		nextContentUuid: '84116454-47b8-47c1-9fbf-c60fc44fc09c',
		numQuestions: 65,
		allottedTimeMins: 100,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapter 4',
	},
	{
		variant: 'final-1',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 130,
		allottedTimeMins: 180,
		type: 'final',
		name: 'Final exam A',
	},
	{
		variant: 'final-2',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 130,
		allottedTimeMins: 180,
		type: 'final',
		name: 'Final exam B',
	},
	{
		variant: 'exam',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 130,
		allottedTimeMins: 180,
		type: 'exam',
		name: 'Series 65 full exam',
	},
];

module.exports = FINRA_SERIES_65_EXAMS;
