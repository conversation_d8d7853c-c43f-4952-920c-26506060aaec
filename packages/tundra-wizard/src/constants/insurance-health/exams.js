const INSURANCE_HEALTH_EXAMS = [
	{
		variant: "exam",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Health full exam",
	},
	{
		variant: "alabama",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Alabama Health exam",
	},
	{
		variant: "alaska",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Alaska Health exam",
	},
	{
		variant: "arizona",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Arizona Health exam",
	},
	{
		variant: "arkansas",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Arkansas Health exam",
	},
	{
		variant: "california",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "California Health exam",
	},
	{
		variant: "colorado",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Colorado Health exam",
	},
	{
		variant: "connecticut",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Connecticut Health exam",
	},
	{
		variant: "delaware",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Delaware Health exam",
	},
	{
		variant: "district-of-columbia",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "District of Columbia Health exam",
	},
	{
		variant: "florida",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Florida Health exam",
	},
	{
		variant: "georgia",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Georgia Health exam",
	},
	{
		variant: "hawaii",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Hawaii Health exam",
	},
	{
		variant: "idaho",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Idaho Health exam",
	},
	{
		variant: "illinois",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Illinois Health exam",
	},
	{
		variant: "indiana",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Indiana Health exam",
	},
	{
		variant: "iowa",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Iowa Health exam",
	},
	{
		variant: "kansas",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Kansas Health exam",
	},
	{
		variant: "kentucky",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Kentucky Health exam",
	},
	{
		variant: "louisiana",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Louisiana Health exam",
	},
	{
		variant: "maine",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Maine Health exam",
	},
	{
		variant: "maryland",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Maryland Health exam",
	},
	{
		variant: "massachusetts",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Massachusetts Health exam",
	},
	{
		variant: "michigan",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Michigan Health exam",
	},
	{
		variant: "minnesota",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Minnesota Health exam",
	},
	{
		variant: "mississippi",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Mississippi Health exam",
	},
	{
		variant: "missouri",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Missouri Health exam",
	},
	{
		variant: "montana",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Montana Health exam",
	},
	{
		variant: "nebraska",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Nebraska Health exam",
	},
	{
		variant: "nevada",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Nevada Health exam",
	},
	{
		variant: "new-hampshire",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "New Hampshire Health exam",
	},
	{
		variant: "new-jersey",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "New Jersey Health exam",
	},
	{
		variant: "new-mexico",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "New Mexico Health exam",
	},
	{
		variant: "new-york",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "New York Health exam",
	},
	{
		variant: "north-carolina",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "North Carolina Health exam",
	},
	{
		variant: "north-dakota",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "North Dakota Health exam",
	},
	{
		variant: "ohio",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Ohio Health exam",
	},
	{
		variant: "oklahoma",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Oklahoma Health exam",
	},
	{
		variant: "oregon",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Oregon Health exam",
	},
	{
		variant: "pennsylvania",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Pennsylvania Health exam",
	},
	{
		variant: "rhode-island",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Rhode Island Health exam",
	},
	{
		variant: "south-carolina",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "South Carolina Health exam",
	},
	{
		variant: "south-dakota",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "South Dakota Health exam",
	},
	{
		variant: "tennessee",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Tennessee Health exam",
	},
	{
		variant: "texas",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Texas Health exam",
	},
	{
		variant: "utah",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Utah Health exam",
	},
	{
		variant: "vermont",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Vermont Health exam",
	},
	{
		variant: "virginia",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Virginia Health exam",
	},
	{
		variant: "washington",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Washington Health exam",
	},
	{
		variant: "west-virginia",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "West Virginia Health exam",
	},
	{
		variant: "wisconsin",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Wisconsin Health exam",
	},
	{
		variant: "wyoming",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Wyoming Health exam",
	},
];

module.exports = { INSURANCE_HEALTH_EXAMS };
