const _ = require("lodash");

const GRE_EXAMS = [
	{
		variant: "quant-short",
		nextContentUuid: null,
		numQuestions: 12,
		allottedTimeMins: 21,
		type: "exam",
		name: "Quant (short)",
	},
	{
		variant: "quant-long",
		nextContentUuid: null,
		numQuestions: 15,
		allottedTimeMins: 26,
		type: "exam",
		name: "Quant (long)",
	},
	{
		variant: "verbal-short",
		nextContentUuid: null,
		numQuestions: 12,
		allottedTimeMins: 18,
		type: "exam",
		name: "Verbal (short)",
		count: 8,
	},
	{
		variant: "verbal-long",
		nextContentUuid: null,
		numQuestions: 15,
		allottedTimeMins: 23,
		type: "exam",
		name: "Verbal (long)",
		count: 16,
	},
	{
		variant: "issue",
		nextContentUuid: null,
		numQuestions: null,
		allottedTimeMins: 30,
		type: "essay",
		name: "Issue essay",
	},
];

const GRE_EXAMS_BY_VARIANT = _.keyBy(GRE_EXAMS, "variant"); //?
GRE_EXAMS_BY_VARIANT["argument"] = {
	variant: "argument",
	numQuestions: null,
	allottedTimeMins: 30,
};
GRE_EXAMS_BY_VARIANT["quant"] = {
	variant: "quant",
	numQuestions: 20,
	allottedTimeMins: 35,
};
GRE_EXAMS_BY_VARIANT["quant-v2"] = {
	variant: "quant-v2",
	numQuestions: 14,
	allottedTimeMins: 24,
};
GRE_EXAMS_BY_VARIANT["verbal"] = {
	variant: "verbal",
	numQuestions: 20,
	allottedTimeMins: 30,
};
GRE_EXAMS_BY_VARIANT["verbal-v2a"] = {
	variant: "verbal-v2a",
	numQuestions: 13,
	allottedTimeMins: 20,
};
GRE_EXAMS_BY_VARIANT["verbal-v2b"] = {
	variant: "verbal-v2b",
	numQuestions: 14,
	allottedTimeMins: 21,
};

module.exports = { GRE_EXAMS, GRE_EXAMS_BY_VARIANT };
