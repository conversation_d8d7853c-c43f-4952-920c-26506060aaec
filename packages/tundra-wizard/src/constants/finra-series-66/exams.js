const FINRA_SERIES_66_EXAMS = [
	{
		variant: 'chapter-2',
		rootContentUuid: 'a2bc8583-0a50-40da-83a7-59dcaa0825f1',
		nextContentUuid: '1d2e6d08-83b9-48ca-974b-de7cd7962bf2',
		numQuestions: 50,
		allottedTimeMins: 75,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapter 1',
	},
	{
		variant: 'chapter-3',
		rootContentUuid: 'b285c25a-f02a-4f1a-87d0-d047f8589be1',
		nextContentUuid: '8d5cc4e0-1932-4873-8b01-c99f4a782f08',
		numQuestions: 50,
		allottedTimeMins: 75,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapter 2',
	},
	{
		variant: 'chapters-2-to-3',
		rootContentUuid: null,
		nextContentUuid: '8d5cc4e0-1932-4873-8b01-c99f4a782f08',
		numQuestions: 50,
		allottedTimeMins: 75,
		type: 'midterm',
		name: 'Midterm',
		title: 'Chapters 1 to 2',
	},
	{
		variant: 'chapter-4',
		rootContentUuid: 'd45ca977-87c1-4edc-8830-83250bb518c6',
		nextContentUuid: '3b0be996-b819-450c-a4cd-36f41db77162',
		numQuestions: 30,
		allottedTimeMins: 45,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapter 3',
	},
	{
		variant: 'chapter-5',
		rootContentUuid: 'af2ef699-b962-480d-b85c-b1ba6d648148',
		nextContentUuid: '2f1e7588-65e4-4381-af0a-3a755c2732d9',
		numQuestions: 50,
		allottedTimeMins: 75,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapter 4',
	},
	{
		variant: 'final-1',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 150,
		type: 'final',
		name: 'Final exam A',
	},
	{
		variant: 'final-2',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 150,
		type: 'final',
		name: 'Final exam B',
	},
	{
		variant: 'exam',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 150,
		type: 'exam',
		name: 'Series 66 full exam',
	},
];

module.exports = FINRA_SERIES_66_EXAMS;
