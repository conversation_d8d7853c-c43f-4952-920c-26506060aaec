const _ = require("lodash");
const { US_STATES_BY_VARIANT } = require("../usStates");

const INSURANCE_PERSONAL_LINES_EXAMS = [
	{
		variant: "exam",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "PL full exam",
	},
	..._.map(US_STATES_BY_VARIANT, (state, variant) => ({
		variant,
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: `${state} PL exam`,
	})),
];

module.exports = { INSURANCE_PERSONAL_LINES_EXAMS };
