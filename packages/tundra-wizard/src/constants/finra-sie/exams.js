const _ = require('lodash');

const FINRA_SIE_EXAMS = [
	{
		variant: 'common-stock',
		rootContentUuid: '04823579-fde5-4d30-93eb-7427f5ff4188',
		nextContentUuid: '04b074e4-74a0-478c-b29f-a2bdf2abfceb',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 1',
		title: 'Common stock',
	},
	{
		variant: 'preferred-stock',
		rootContentUuid: 'c31711ed-7746-4056-adb9-286e3bffcb5a',
		nextContentUuid: 'cd2424a0-b518-4c10-9f3c-2119028b3526',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 2',
		title: 'Preferred stock',
	},
	{
		variant: 'bond-fundamentals',
		rootContentUuid: '4a9368c1-5056-4fee-8a80-dc1f906ffb64',
		nextContentUuid: '374eb993-d99b-40b9-8786-b43973edc8e1',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 3',
		title: 'Bond fundamentals',
	},
	{
		variant: 'corporate-debt',
		rootContentUuid: 'd239b17e-4544-4315-afd7-cbadebca6663',
		nextContentUuid: '3ecde4f5-5c46-43f7-adf9-bc5576eb2e11',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 4',
		title: 'Corporate debt',
	},
	{
		variant: 'chapters-1-to-4',
		rootContentUuid: null,
		nextContentUuid: '3ecde4f5-5c46-43f7-adf9-bc5576eb2e11',
		numQuestions: 40,
		allottedTimeMins: 60,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapters 1 to 4',
	},
	{
		variant: 'municipal-debt',
		rootContentUuid: 'f0e23833-1e92-443a-af0f-59e40e579087',
		nextContentUuid: 'f462f8d2-2f1c-4845-8783-b5c0bdbef55c',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 5',
		title: 'Municipal debt',
	},
	{
		variant: 'us-government-debt',
		rootContentUuid: '8c37b7ba-2f97-4b87-ae81-6523b9f86fab',
		nextContentUuid: 'f3ca48c4-71c7-4f56-ad20-48cc0314dfda',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 6',
		title: 'US government debt',
	},
	{
		variant: 'investment-companies',
		rootContentUuid: 'c08403e2-efcc-4657-adb0-ebd850480332',
		nextContentUuid: '29706006-6e91-42b9-a614-fe448603c596',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 7',
		title: 'Investment companies',
	},
	{
		variant: 'alternative-pooled-investments',
		rootContentUuid: '598e098c-9d17-4abf-af53-0a778ab903f0',
		nextContentUuid: 'f7c1031a-a67d-48b9-9494-b24d15151441',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 8',
		title: 'Alternative pooled investments',
	},
	{
		variant: 'chapters-5-to-8',
		rootContentUuid: null,
		nextContentUuid: 'f7c1031a-a67d-48b9-9494-b24d15151441',
		numQuestions: 40,
		allottedTimeMins: 60,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapters 5 to 8',
	},
	{
		variant: 'chapters-1-to-8',
		rootContentUuid: null,
		nextContentUuid: 'f7c1031a-a67d-48b9-9494-b24d15151441',
		numQuestions: 40,
		allottedTimeMins: 60,
		type: 'midterm',
		name: 'Midterm 1',
		title: 'Chapters 1 to 8',
	},
	{
		variant: 'options',
		rootContentUuid: '1ea60ecb-e966-4259-9ac8-d69d6912ea86',
		nextContentUuid: '84792d8e-7353-4541-b288-170b62e094bb',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 9',
		title: 'Options',
	},
	{
		variant: 'taxes',
		rootContentUuid: 'd7a1cff8-fc36-4c63-a4b8-7cf599d8c284',
		nextContentUuid: '9f24aafe-416b-4de7-97a2-b9d8fb96957a',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 10',
		title: 'Taxes',
	},
	{
		variant: 'the-primary-market',
		rootContentUuid: '8ec852cc-30ee-4f5e-85d9-28724b67e4a3',
		nextContentUuid: '2c544a62-9c84-44d8-946a-f93ed58fb83a',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 11',
		title: 'The primary market',
	},
	{
		variant: 'chapters-9-to-11',
		rootContentUuid: null,
		nextContentUuid: '2c544a62-9c84-44d8-946a-f93ed58fb83a',
		numQuestions: 40,
		allottedTimeMins: 60,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapters 9 to 11',
	},
	{
		variant: 'the-secondary-market',
		rootContentUuid: '54db84de-be7b-4b80-a780-ffe9812ed421',
		nextContentUuid: '267dd199-c585-46b7-ac92-41ea977e9918',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 12',
		title: 'The secondary market',
	},
	{
		variant: 'brokerage-accounts',
		rootContentUuid: '5dff287f-5bb4-4c13-a3f6-b059df999f48',
		nextContentUuid: '3bd2ef51-1b21-43f8-9ce2-4cd787a8f3b5',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 13',
		title: 'Brokerage accounts',
	},
	{
		variant: 'retirement-and-education-plans',
		rootContentUuid: 'db8c739f-f153-41d6-b0e8-74cc073a1622',
		nextContentUuid: 'cb7ca759-7ded-476d-b821-b5dedab83b88',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 14',
		title: 'Retirement and education plans',
	},
	{
		variant: 'rules-and-ethics',
		rootContentUuid: 'fed700f2-75cd-4255-9b95-f721c0071c64',
		nextContentUuid: 'ab15190e-b6ee-4ea4-b115-59c082ad749e',
		numQuestions: 20,
		allottedTimeMins: 30,
		type: 'chapter',
		name: 'Chapter 15',
		title: 'Rules and ethics',
	},
	{
		variant: 'chapters-12-to-15',
		rootContentUuid: null,
		nextContentUuid: 'ab15190e-b6ee-4ea4-b115-59c082ad749e',
		numQuestions: 40,
		allottedTimeMins: 60,
		type: 'checkpoint',
		name: 'Checkpoint',
		title: 'Chapters 12 to 15',
	},
	{
		variant: 'chapters-9-to-15',
		rootContentUuid: null,
		nextContentUuid: 'ab15190e-b6ee-4ea4-b115-59c082ad749e',
		numQuestions: 40,
		allottedTimeMins: 60,
		type: 'midterm-2',
		name: 'Midterm 2',
		title: 'Chapters 9 to 15',
	},
	{
		variant: 'final-1',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 75,
		allottedTimeMins: 105,
		type: 'final',
		name: 'Final exam A',
	},
	{
		variant: 'final-2',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 75,
		allottedTimeMins: 105,
		type: 'final',
		name: 'Final exam B',
	},
	{
		variant: 'exam',
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 75,
		allottedTimeMins: 105,
		type: 'exam',
		name: 'SIE full exam',
	},
];

// 20231128JF: handles slight changes in variants between v1 and v2 study plans (just for SIE)
const FINRA_SIE_EXAMS_BY_VARIANT = _.keyBy(FINRA_SIE_EXAMS, 'variant');
FINRA_SIE_EXAMS_BY_VARIANT['alternative-investments'] = FINRA_SIE_EXAMS_BY_VARIANT['alternative-pooled-investments'];
FINRA_SIE_EXAMS_BY_VARIANT['primary-market'] = FINRA_SIE_EXAMS_BY_VARIANT['the-primary-market'];
FINRA_SIE_EXAMS_BY_VARIANT['secondary-market'] = FINRA_SIE_EXAMS_BY_VARIANT['the-secondary-market'];
FINRA_SIE_EXAMS_BY_VARIANT['chapters-1-to-8--randomized'] = {
	variant: 'chapters-1-to-8--randomized',
	numQuestions: 40,
	allottedTimeMins: 60,
	name: 'Chapters 1 to 8',
	type: 'midterm',
};
FINRA_SIE_EXAMS_BY_VARIANT['chapters-9-to-15--randomized'] = {
	variant: 'chapters-9-to-15--randomized',
	numQuestions: 40,
	allottedTimeMins: 60,
	name: 'Chapters 9 to 15',
	type: 'midterm-2',
};

module.exports = { FINRA_SIE_EXAMS, FINRA_SIE_EXAMS_BY_VARIANT };
