const INSURANCE_LIFE_HEALTH_EXAMS = [
	{
		variant: "exam",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "L&H full exam",
	},
	{
		variant: "alabama",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Alabama L&H exam",
	},
	{
		variant: "alaska",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Alaska L&H exam",
	},
	{
		variant: "arizona",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Arizona L&H exam",
	},
	{
		variant: "arkansas",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Arkansas L&H exam",
	},
	{
		variant: "california",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "California L&H exam",
	},
	{
		variant: "colorado",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Colorado L&H exam",
	},
	{
		variant: "connecticut",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Connecticut L&H exam",
	},
	{
		variant: "delaware",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Delaware L&H exam",
	},
	{
		variant: "district-of-columbia",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "District of Columbia L&H exam",
	},
	{
		variant: "florida",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Florida L&H exam",
	},
	{
		variant: "georgia",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Georgia L&H exam",
	},
	{
		variant: "hawaii",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Hawaii L&H exam",
	},
	{
		variant: "idaho",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Idaho L&H exam",
	},
	{
		variant: "illinois",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Illinois L&H exam",
	},
	{
		variant: "indiana",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Indiana L&H exam",
	},
	{
		variant: "iowa",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Iowa L&H exam",
	},
	{
		variant: "kansas",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Kansas L&H exam",
	},
	{
		variant: "kentucky",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Kentucky L&H exam",
	},
	{
		variant: "louisiana",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Louisiana L&H exam",
	},
	{
		variant: "maine",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Maine L&H exam",
	},
	{
		variant: "maryland",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Maryland L&H exam",
	},
	{
		variant: "massachusetts",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Massachusetts L&H exam",
	},
	{
		variant: "michigan",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Michigan L&H exam",
	},
	{
		variant: "minnesota",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Minnesota L&H exam",
	},
	{
		variant: "mississippi",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Mississippi L&H exam",
	},
	{
		variant: "missouri",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Missouri L&H exam",
	},
	{
		variant: "montana",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Montana L&H exam",
	},
	{
		variant: "nebraska",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Nebraska L&H exam",
	},
	{
		variant: "nevada",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Nevada L&H exam",
	},
	{
		variant: "new-hampshire",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "New Hampshire L&H exam",
	},
	{
		variant: "new-jersey",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "New Jersey L&H exam",
	},
	{
		variant: "new-mexico",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "New Mexico L&H exam",
	},
	{
		variant: "new-york",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "New York L&H exam",
	},
	{
		variant: "north-carolina",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "North Carolina L&H exam",
	},
	{
		variant: "north-dakota",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "North Dakota L&H exam",
	},
	{
		variant: "ohio",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Ohio L&H exam",
	},
	{
		variant: "oklahoma",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Oklahoma L&H exam",
	},
	{
		variant: "oregon",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Oregon L&H exam",
	},
	{
		variant: "pennsylvania",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Pennsylvania L&H exam",
	},
	{
		variant: "rhode-island",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Rhode Island L&H exam",
	},
	{
		variant: "south-carolina",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "South Carolina L&H exam",
	},
	{
		variant: "south-dakota",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "South Dakota L&H exam",
	},
	{
		variant: "tennessee",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Tennessee L&H exam",
	},
	{
		variant: "texas",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Texas L&H exam",
	},
	{
		variant: "utah",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Utah L&H exam",
	},
	{
		variant: "vermont",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Vermont L&H exam",
	},
	{
		variant: "virginia",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Virginia L&H exam",
	},
	{
		variant: "washington",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Washington L&H exam",
	},
	{
		variant: "west-virginia",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "West Virginia L&H exam",
	},
	{
		variant: "wisconsin",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Wisconsin L&H exam",
	},
	{
		variant: "wyoming",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Wyoming L&H exam",
	},
];

module.exports = { INSURANCE_LIFE_HEALTH_EXAMS };
