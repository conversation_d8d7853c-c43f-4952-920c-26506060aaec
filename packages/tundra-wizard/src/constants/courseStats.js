const _ = require("lodash");

const TEXTBOOK_TYPE = {
	concise: "concise",
	reference: "reference",
	standard: "standard",
};

const COURSE_STATS = {
	act: {
		hasPracticeExams: true,
		numChapterQuizzes: 1000,
		numEnglishSections: 3,
		numPracticeExams: 3,
		numReadingSections: 3,
		numScienceSections: 2,
		numVideos: 10,
		shortName: "ACT",
	},
	"amc-8": {
		hasPracticeExams: false,
		shortName: "AMC 8",
	},
	"amc-10": {
		hasPracticeExams: false,
		shortName: "AMC 10",
	},
	"amc-12": {
		hasPracticeExams: false,
		shortName: "AMC 12",
		textbook: TEXTBOOK_TYPE.reference,
	},
	clt: {
		hasPracticeExams: true,
		numChapterQuizzes: 200,
		numGrammarWritingSections: 3,
		numPracticeExams: 3,
		numQuantSections: 3,
		numVerbalSections: 3,
		shortName: "CLT",
	},
	"finra-sie": {
		hasAudio: true,
		hasPracticeExams: true,
		numChapterQuizzes: 2000,
		numPracticeExams: 35,
		numVideos: 36,
		shortName: "SIE",
	},
	"finra-series-6": {
		hasAudio: true,
		hasPracticeExams: true,
		numChapterQuizzes: 800,
		numPracticeExams: 35,
		numVideos: 14,
		shortName: "Series 6",
	},
	"finra-series-7": {
		hasAudio: true,
		hasPracticeExams: true,
		numChapterQuizzes: 2000,
		numPracticeExams: 35,
		numVideos: 44,
		shortName: "Series 7",
	},
	"finra-series-9": {
		hasAudio: true,
		hasPracticeExams: true,
		numChapterQuizzes: 2000,
		numPracticeExams: 18,
		numVideos: 20,
		shortName: "Series 9",
	},
	"finra-series-63": {
		hasAudio: true,
		hasPracticeExams: true,
		numChapterQuizzes: 300,
		numPracticeExams: 6,
		numVideos: 12,
		shortName: "Series 63",
	},
	"finra-series-65": {
		hasAudio: true,
		hasPracticeExams: true,
		numChapterQuizzes: 1300,
		numPracticeExams: 20,
		numVideos: 44,
		shortName: "Series 65",
	},
	"finra-series-66": {
		hasAudio: true,
		hasPracticeExams: true,
		numChapterQuizzes: 1200,
		numPracticeExams: 26,
		numVideos: 47,
		shortName: "Series 66",
	},
	gre: {
		hasPracticeExams: true,
		numPracticeExams: 10,
		shortName: "GRE",
	},
	"gre-v2": {
		hasPracticeExams: true,
		numPracticeExams: 30,
		numQuant: 200,
		numVerbal: 400,
		numVideos: 16,
		numVerbalSections: 30,
		shortName: "GRE",
	},
	"insurance-life-health": {
		hasPracticeExams: true,
		hasSupplemental: true,
		numChapterQuizzes: 676,
		numPracticeExams: 5,
		shortName: "Life & Health (L&H)",
	},
	"insurance-life": {
		hasPracticeExams: true,
		hasSupplemental: true,
		numChapterQuizzes: 329,
		numPracticeExams: 5,
		shortName: "Life",
	},
	"insurance-health": {
		hasPracticeExams: true,
		hasSupplemental: true,
		numChapterQuizzes: 469,
		numPracticeExams: 5,
		shortName: "Health",
	},
	"usmle-step-1": {
		hasPracticeExams: false,
		numChapterQuizzes: 1400,
		numVideos: 24,
		shortName: "USMLE Step 1",
		textbook: TEXTBOOK_TYPE.concise,
	},
};

module.exports = COURSE_STATS;
