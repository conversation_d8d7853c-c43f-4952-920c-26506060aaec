const INSURANCE_LIFE_EXAMS = [
	{
		variant: "exam",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Life full exam",
	},
	{
		variant: "alabama",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Alabama Life exam",
	},
	{
		variant: "alaska",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Alaska Life exam",
	},
	{
		variant: "arizona",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Arizona Life exam",
	},
	{
		variant: "arkansas",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Arkansas Life exam",
	},
	{
		variant: "california",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "California Life exam",
	},
	{
		variant: "colorado",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Colorado Life exam",
	},
	{
		variant: "connecticut",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Connecticut Life exam",
	},
	{
		variant: "delaware",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Delaware Life exam",
	},
	{
		variant: "district-of-columbia",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "District of Columbia Life exam",
	},
	{
		variant: "florida",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Florida Life exam",
	},
	{
		variant: "georgia",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Georgia Life exam",
	},
	{
		variant: "hawaii",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Hawaii Life exam",
	},
	{
		variant: "idaho",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Idaho Life exam",
	},
	{
		variant: "illinois",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Illinois Life exam",
	},
	{
		variant: "indiana",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Indiana Life exam",
	},
	{
		variant: "iowa",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Iowa Life exam",
	},
	{
		variant: "kansas",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Kansas Life exam",
	},
	{
		variant: "kentucky",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Kentucky Life exam",
	},
	{
		variant: "louisiana",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Louisiana Life exam",
	},
	{
		variant: "maine",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Maine Life exam",
	},
	{
		variant: "maryland",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Maryland Life exam",
	},
	{
		variant: "massachusetts",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Massachusetts Life exam",
	},
	{
		variant: "michigan",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Michigan Life exam",
	},
	{
		variant: "minnesota",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Minnesota Life exam",
	},
	{
		variant: "mississippi",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Mississippi Life exam",
	},
	{
		variant: "missouri",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Missouri Life exam",
	},
	{
		variant: "montana",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Montana Life exam",
	},
	{
		variant: "nebraska",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Nebraska Life exam",
	},
	{
		variant: "nevada",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Nevada Life exam",
	},
	{
		variant: "new-hampshire",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "New Hampshire Life exam",
	},
	{
		variant: "new-jersey",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "New Jersey Life exam",
	},
	{
		variant: "new-mexico",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "New Mexico Life exam",
	},
	{
		variant: "new-york",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "New York Life exam",
	},
	{
		variant: "north-carolina",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "North Carolina Life exam",
	},
	{
		variant: "north-dakota",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "North Dakota Life exam",
	},
	{
		variant: "ohio",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Ohio Life exam",
	},
	{
		variant: "oklahoma",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Oklahoma Life exam",
	},
	{
		variant: "oregon",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Oregon Life exam",
	},
	{
		variant: "pennsylvania",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Pennsylvania Life exam",
	},
	{
		variant: "rhode-island",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Rhode Island Life exam",
	},
	{
		variant: "south-carolina",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "South Carolina Life exam",
	},
	{
		variant: "south-dakota",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "South Dakota Life exam",
	},
	{
		variant: "tennessee",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Tennessee Life exam",
	},
	{
		variant: "texas",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Texas Life exam",
	},
	{
		variant: "utah",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Utah Life exam",
	},
	{
		variant: "vermont",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Vermont Life exam",
	},
	{
		variant: "virginia",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Virginia Life exam",
	},
	{
		variant: "washington",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Washington Life exam",
	},
	{
		variant: "west-virginia",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "West Virginia Life exam",
	},
	{
		variant: "wisconsin",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Wisconsin Life exam",
	},
	{
		variant: "wyoming",
		rootContentUuid: null,
		nextContentUuid: null,
		numQuestions: 100,
		allottedTimeMins: 120,
		type: "exam",
		name: "Wyoming Life exam",
	},
];

module.exports = { INSURANCE_LIFE_EXAMS };
