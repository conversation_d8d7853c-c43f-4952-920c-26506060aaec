const _ = require("lodash");

const PRODUCT_AUTHOR_SLUG_MAP = {
	act: "odyssey",
	"amc-10": "wescarroll",
	"amc-12": "wescarroll",
	"amc-8": "wescarroll",
	clt: "clayborne",
	"finra-bundle-sie-s7": "basic-wisdom",
	"finra-everything": "basic-wisdom",
	"finra-series-6": "basic-wisdom",
	"finra-series-9": "basic-wisdom",
	"finra-series-10": "basic-wisdom",
	"finra-series-24": "basic-wisdom",
	"finra-series-63": "basic-wisdom",
	"finra-series-65": "basic-wisdom",
	"finra-series-66": "basic-wisdom",
	"finra-series-7": "basic-wisdom",
	"finra-sie": "basic-wisdom",
	gre: "stellar",
	"gre-v2": "mr",
	"insurance-life-health": "matt-williams",
	"insurance-life": "matt-williams",
	"insurance-health": "matt-williams",
	"usmle-step-1": "graceusmle",
};

const PRODUCTS_FOR_AUTHOR = _.reduce(
	PRODUCT_AUTHOR_SLUG_MAP,
	(acc, author, product) => {
		acc[author] = acc[author] || [];
		acc[author].push(product);
		return acc;
	},
	{}
);

const PARTNERS = {
	aceTheGre: "acethegre",
	amizoa: "amizoa",
	anking: "anking",
	mattWilliams: "matt-williams",
	mr: "mr",
	arthur: "arthur",
	benWhite: "benwhite",
	capitalAdvantage: "capital-advantage",
	cftNow: "cft-now",
	clayborne: "clayborne",
	crush: "crush",
	crushGre: "crushgre",
	discount: "discount",
	dotdash: "dotdash",
	edge: "edg3",
	eduReviewer: "edureviewer",
	examStrategist: "examstrategist",
	graceUsmle: "graceusmle",
	guru99: "guru99",
	myGre: "mygre",
	myGuru: "myguru",
	odyssey: "odyssey",
	placementBoat: "placementboat",
	series7ExamTutor: "series7examtutor",
	stellar: "stellar",
	testGuide: "testguide",
	tpi: "tpi",
	wesCarroll: "wescarroll",
	wikiJob: "wikijob",
	wso: "wso",
};

const DATA = {
	permissions: {
		"<EMAIL>": PARTNERS.clayborne,
		"<EMAIL>": PARTNERS.clayborne,
		"<EMAIL>": PARTNERS.mr,
		"<EMAIL>": PARTNERS.odyssey,
		"<EMAIL>": PARTNERS.clayborne,
		"<EMAIL>": PARTNERS.graceUsmle,
		"<EMAIL>": PARTNERS.wesCarroll,
		"<EMAIL>": PARTNERS.mattWilliams,
	},
	partners: {
		[PARTNERS.aceTheGre]: {
			authorShare: 0,
			affiliateShare: 0.3,
			products: [],
		},
		[PARTNERS.amizoa]: {
			authorShare: 0,
			affiliateShare: 0.3,
			products: [],
		},
		[PARTNERS.anking]: {
			authorShare: 0,
			affiliateShare: 0.2,
			products: [],
		},
		[PARTNERS.arthur]: {
			authorShare: 0,
			affiliateShare: 0.4,
			products: [],
		},
		[PARTNERS.benWhite]: {
			authorShare: 0,
			affiliateShare: 0.25,
			products: [],
		},
		[PARTNERS.capitalAdvantage]: {
			authorShare: 0,
			affiliateShare: 0.5,
			products: [],
		},
		[PARTNERS.cftNow]: {
			authorShare: 0,
			affiliateShare: 0.2,
			products: [],
		},
		[PARTNERS.clayborne]: {
			authorShare: 0.35,
			affiliateShare: 0.2,
			products: PRODUCTS_FOR_AUTHOR[PARTNERS.clayborne],
		},
		[PARTNERS.crush]: {
			authorShare: 0,
			affiliateShare: 0.3,
			products: [],
		},
		[PARTNERS.crushGre]: {
			authorShare: 0,
			affiliateShare: 0.4,
			products: [],
		},
		[PARTNERS.discount]: {
			authorShare: 0,
			affiliateShare: 0.25,
			products: [],
		},
		[PARTNERS.dotdash]: {
			authorShare: 0,
			affiliateShare: 0.25,
			products: [],
		},
		[PARTNERS.edge]: {
			authorShare: 0,
			affiliateShare: 0.3,
			products: [],
		},
		[PARTNERS.eduReviewer]: {
			authorShare: 0,
			affiliateShare: 0.25,
			products: [],
		},
		[PARTNERS.examStrategist]: {
			authorShare: 0,
			affiliateShare: 0.25,
			products: [],
		},
		[PARTNERS.graceUsmle]: {
			authorShare: 0.4,
			affiliateShare: 0.3333,
			products: PRODUCTS_FOR_AUTHOR[PARTNERS.graceUsmle],
		},
		[PARTNERS.guru99]: {
			authorShare: 0,
			affiliateShare: 0.4,
			products: [],
		},
		[PARTNERS.mattWilliams]: {
			authorShare: 0.3,
			affiliateShare: 0.35,
			products: PRODUCTS_FOR_AUTHOR[PARTNERS.mattWilliams],
		},
		[PARTNERS.mr]: {
			authorShare: 0.08,
			affiliateShare: 0.2174,
			products: PRODUCTS_FOR_AUTHOR[PARTNERS.mr],
		},
		[PARTNERS.myGuru]: {
			authorShare: 0,
			affiliateShare: 0.3,
			products: [],
		},
		[PARTNERS.myGre]: {
			authorShare: 0,
			affiliateShare: 0.3,
			products: [],
		},
		[PARTNERS.odyssey]: {
			authorShare: 0.4, // 20220815JP: Sliding starts at $100k
			affiliateShare: 0.3333,
			products: PRODUCTS_FOR_AUTHOR[PARTNERS.odyssey],
		},
		[PARTNERS.placementBoat]: {
			authorShare: 0,
			affiliateShare: 0.5,
			products: [],
		},
		[PARTNERS.series7ExamTutor]: {
			authorShare: 0,
			affiliateShare: 0.5,
			products: [],
		},
		[PARTNERS.stellar]: {
			authorShare: 0.4,
			affiliateShare: 0,
			products: PRODUCTS_FOR_AUTHOR[PARTNERS.stellar],
		},
		[PARTNERS.testGuide]: {
			authorShare: 0,
			affiliateShare: 0.25,
			products: [],
		},
		[PARTNERS.tpi]: {
			authorShare: 0,
			affiliateShare: 0.3,
			products: [],
		},
		[PARTNERS.wesCarroll]: {
			authorShare: 0.4,
			affiliateShare: 0,
			products: PRODUCTS_FOR_AUTHOR[PARTNERS.wesCarroll],
		},
		[PARTNERS.wikiJob]: {
			authorShare: 0,
			affiliateShare: 0.3,
			products: [],
		},
		[PARTNERS.wso]: {
			authorShare: 0,
			affiliateShare: 0.5,
			products: [],
		},
	},
};

const getPartnerType = ({ partnerSlug }) => {
	if (partnerSlug === "achievable") {
		return "achievable";
	}

	if (PRODUCTS_FOR_AUTHOR[partnerSlug]) {
		return "author";
	}

	return "other";
};

module.exports = { PRODUCT_AUTHOR_SLUG_MAP, PRODUCTS_FOR_AUTHOR, DATA, getPartnerType };
