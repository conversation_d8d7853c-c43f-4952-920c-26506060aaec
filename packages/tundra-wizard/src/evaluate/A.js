const constant = require("./constant");
const format = require("./format");
const math = require("./math");
const number = require("./number");
const random = require("./random");
const sample = require("./sample");
const util = require("./util");
const geometry = require("./geometry");
const vocab = require("./vocab");

const seededRandom = require("./seededRandom");

const createA = (opts = {}) => {
	const { seed } = opts;

	const x = {
		seed,
		random: seededRandom.seededRandom(seed),
	};

	return {
		c: constant({ x }),
		f: format({ x }),
		m: math({ x }),
		n: number({ x }),
		r: random({ x }),
		s: sample({ x }),
		u: util({ x }),
		geometry: geometry({ x }),
		vocab: vocab({ x }),
	};
};

module.exports = createA;
