const weekdays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];

// 20190109JP: Keep this Monday-indexed to make calculations easier.
const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

const colors = ["red", "orange", "yellow", "green", "blue", "indigo", "violet"];

const countries = [
	"Austria",
	"Brazil",
	"Canada",
	"Denmark",
	"Egypt",
	"France",
	"Greece",
	"Haiti",
	"India",
	"Japan",
	"Kenya",
	"Libya",
	"Mexico",
	"Nepal",
	"Oman",
	"Peru",
	"Qatar",
	"Russia",
	"Samoa",
	"Tonga",
	"Uruguay",
	"Vietnam",
	"Yemen",
	"Zambia",
];

const chartColors = {
	red: "rgb(255, 99, 132)",
	orange: "rgb(255, 159, 64)",
	yellow: "rgb(255, 205, 86)",
	green: "rgb(75, 192, 192)",
	blue: "rgb(54, 162, 235)",
	purple: "rgb(153, 102, 255)",
	// grey: 'rgb(201, 203, 207)'
};

const init = () => ({
	chartColors,
	colors,
	countries,
	days,
	weekdays,
});

module.exports = init;
