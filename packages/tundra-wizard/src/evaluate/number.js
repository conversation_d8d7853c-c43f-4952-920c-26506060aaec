// 20250518JP: By rounding twice, we can mitigate floating point errors.
//   See the tests for examples.
const round = (num, places = 0) => {
	const parsedOriginalNum = parseFloat(num);
	const fixedNumExtra = parsedOriginalNum.toFixed(places + 2);
	const floatFixedNumExtra = parseFloat(fixedNumExtra);
	const fixedNum = floatFixedNumExtra.toFixed(places);
	const parsedNum = parseFloat(fixedNum);
	return parsedNum;
};

const init = () => ({
	r: round,
});

module.exports = init;
