const lodash = require("lodash");
const seedrandom = require("seedrandom");

const init = ({ x }) => {
	const { seed } = x;
	seedrandom(seed, { global: true });
	const _ = lodash.runInContext();

	const intInclusive = (min, max) => {
		return Math.floor(x.random() * (max - min + 1)) + min;
	};

	const evenIntInclusive = (min, max) => {
		let num;
		do {
			num = intInclusive(min, max);
		} while (num % 2 !== 0);
		return num;
	};

	const oddIntInclusive = (min, max) => {
		let num;
		do {
			num = intInclusive(min, max);
		} while (num % 2 === 0);
		return num;
	};

	const decimalInclusive = (min, max) => {
		return intInclusive(min * 100, max * 100) / 100;
	};

	const sample = (...args) => {
		return _.sample.apply(this, args);
	};

	const sampleGrouped = (arrays, num = 1) => {
		if (!Array.isArray(arrays)) {
			throw new Error("Expected an array as the first argument");
		}

		if (!_.every(arrays, (elem) => Array.isArray(elem))) {
			throw new Error("Expected an array of arrays as the first argument");
		}

		const eligibleArrays = arrays.filter((array) => array.length > 0);
		const sampledArrays = _.sampleSize(eligibleArrays, num);

		const result = [];
		for (let i = 0; i < sampledArrays.length; i += 1) {
			const currentArray = sampledArrays[i];
			const sampledValue = _.sample(currentArray);
			result.push(sampledValue);
		}

		return result;
	};

	const sampleSize = (...args) => {
		return _.sampleSize.apply(this, args);
	};

	const sampleTiered = (arrays, num) => {
		let remaining = num;
		let result = [];

		for (let i = 0; i < arrays.length; i += 1) {
			const currentArray = arrays[i];
			const selected = _.sampleSize(currentArray, remaining);
			result = result.concat(selected);
			remaining = num - result.length;

			if (remaining === 0) break;
		}

		return _.shuffle(result);
	};

	const sign = () => {
		return _.sample([-1, 1]);
	};

	const shuffle = (...args) => {
		return _.shuffle.apply(this, args);
	};

	return {
		di: decimalInclusive,
		decimalInclusive,
		ii: intInclusive,
		intInclusive,
		evenIntInclusive,
		oddIntInclusive,
		sample,
		sampleGrouped,
		sampleSize,
		sampleTiered,
		shuffle,
		sign,
	};
};

module.exports = init;
