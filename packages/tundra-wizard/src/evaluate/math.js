const _ = require("lodash");

const gcd = (a, b) => {
	return b ? gcd(b, a % b) : a;
};

const lcm = (a, b) => {
	return a * (b / gcd(a, b));
};

const mean = (array) => {
	return _.sum(array) / array.length;
};

const median = (array) => {
	const sorted = _.sortBy(array);
	const mid = array.length / 2;
	const result = mid % 1 ? sorted[mid - 0.5] : (sorted[mid - 1] + sorted[mid]) / 2;
	return result;
};

const modes = (array) => {
	const modeObj = {};
	array.forEach((num) => {
		if (!modeObj[num]) {
			modeObj[num] = 0;
		}
		modeObj[num]++;
	});

	let maxFrequency = 0;
	let modes = [];
	for (let num in modeObj) {
		if (modeObj[num] > maxFrequency) {
			modes = [num];
			maxFrequency = modeObj[num];
		} else if (modeObj[num] === maxFrequency) {
			modes.push(num);
		}
	}

	const numericModes = modes.map((mode) => parseFloat(mode, 10));
	return numericModes;
};

const factors = (number) => Array.from(Array(number + 1), (_, i) => i).filter((i) => number % i === 0);

const pm = (val) => {
	if (val < 0) {
		return val;
	}

	return `+${val}`;
};

const pmx = (val, x) => {
	if (val === 0) {
		return "";
	}

	if (val === 1 || val === -1) {
		if (!x) {
			return pm(val);
		}

		if (val === 1) {
			return `+${x}`;
		}

		return `-${x}`;
	}

	if (!x) {
		return pm(val);
	}

	return `${pm(val)}${x}`;
};

const reduce = (numerator, denominator) => {
	const ourGcd = gcd(numerator, denominator);
	return [numerator / ourGcd, denominator / ourGcd];
};

const reduceAndFormat = (numerator, denominator, options = {}) => {
	if (numerator === 0) return 0;

	const [reducedNumerator, reducedDenominator] = reduce(numerator, denominator);

	if (reducedNumerator % reducedDenominator === 0) {
		const integer = reducedNumerator / reducedDenominator;

		if (options.parens) {
			return `(${integer})`;
		}

		return integer;
	} else {
		const sign = reducedNumerator < 0 || reducedDenominator < 0 ? "-" : "";
		const absNumerator = Math.abs(reducedNumerator);
		const absDenominator = Math.abs(reducedDenominator);
		const fraction = `${sign}${absNumerator}/${absDenominator}`;

		if (options.parens) {
			return `(${fraction})`;
		}

		return fraction;
	}
};

const primeFactors = (val) => {
	const factors = [];
	let divisor = 2;

	while (val >= 2) {
		if (val % divisor == 0) {
			factors.push(divisor);
			val = val / divisor;
		} else {
			divisor += 1;
		}
	}

	return factors;
};

const rootReduce = (val) => {
	if (val === 1) {
		return {
			root: 1,
			radicand: null,
			markdown: "1",
		};
	}

	const factors = primeFactors(val);
	const counted = _.countBy(factors);

	let root = 1;
	let radicand = 1;

	_.forEach(counted, (times, n) => {
		while (times >= 2) {
			root *= n;
			times -= 2;
		}

		if (times === 1) {
			radicand *= n;
		}
	});

	if (root === 1) {
		root = null;
	}

	if (radicand === 1) {
		radicand = null;
	}

	let markdown = "";
	if (root) {
		markdown = `${root}${markdown}`;
	}

	if (radicand) {
		markdown = `${markdown}\\sqrt{${radicand}}`;
	}

	return {
		root,
		radicand,
		markdown,
	};
};

const getIsFraction = (val) => /^\d+\/\d+$/.test(val);

const getIsNumber = (val) => {
	if (val == undefined) return false;
	if (val === "") return false;

	return !Number.isNaN(Number(val));
};

/*
  20230612JF: recurring decimal place numbers will not work as expected
    - e.g. '1/3' is ok, but 1/3 (0.3333...) is not ok
    - e.g. '1/2' is ok, 1/2 (0.5) is ok
*/
const splitFraction = (val) => {
	if (!val && val != 0) throw new Error("Invalid argument.");

	const isFraction = getIsFraction(val);
	const isNumber = getIsNumber(val);
	if (!isFraction && !isNumber) throw new Error("Invalid argument.");

	let numerator;
	let denominator;
	if (isNumber && val % 1 !== 0) {
		[numerator, denominator] = reduce(val, 1);
	} else {
		[numerator, denominator = 1] = val
			.toString()
			.split("/")
			.map((str) => Number(str));
	}

	return { numerator, denominator };
};

const divideFractions = (x, y) => {
	const { numerator: a, denominator: b } = splitFraction(x); //?
	const { numerator: c, denominator: d } = splitFraction(y); //?

	const numerator = a * d; //?
	const denominator = b * c; //?

	if (denominator === 0) throw new Error("Cannot divide by zero.");

	const result = reduceAndFormat(numerator, denominator); //?
	return result;
};

const multiplyFractions = (x, y) => {
	const { numerator: a, denominator: b } = splitFraction(x); //?
	const { numerator: c, denominator: d } = splitFraction(y); //?
	const result = reduceAndFormat(a * c, b * d);
	return result;
};

const rootReduceFraction = (val) => {
	if (val == 0) return 0;

	const isFraction = getIsFraction(val);
	if (isFraction) {
		const { numerator, denominator } = splitFraction(val);
		const { markdown: numeratorRoot } = rootReduce(numerator);
		const { markdown: denominatorRoot } = rootReduce(denominator);

		const hasSqrt = _.includes(numeratorRoot, "sqrt") || _.includes(denominatorRoot, "sqrt");
		const result = hasSqrt ? `${numeratorRoot}/${denominatorRoot}` : reduceAndFormat(numeratorRoot, denominatorRoot);
		return result;
	}

	if (!getIsNumber(val)) throw new Error("Invalid argument.");

	const { markdown } = rootReduce(Number(val));
	return markdown;
};

const exponentiateFraction = (val, exponent = 1) => {
	if (!getIsNumber(exponent)) throw new Error("Invalid argument.");

	const isFraction = getIsFraction(val);
	if (isFraction) {
		const { numerator, denominator } = splitFraction(val);
		const result = reduceAndFormat(numerator ** exponent, denominator ** exponent);
		return result;
	}

	if (!getIsNumber(val)) throw new Error("Invalid argument.");

	return val ** exponent;
};

const squareFraction = (val) => {
	return exponentiateFraction(val, 2);
};

const addFractions = (x, y) => {
	const hasFractions = getIsFraction(x) || getIsFraction(y);
	if (hasFractions) {
		const { numerator: a, denominator: b } = splitFraction(x);
		const { numerator: c, denominator: d } = splitFraction(y);
		const numerator = (() => {
			const numerator1 = a * d;
			const numerator2 = c * b;
			return numerator1 + numerator2;
		})();
		const denominator = b * d;

		const result = reduceAndFormat(numerator, denominator);
		return result;
	}

	if (!getIsNumber(x) || !getIsNumber(y)) {
		throw new Error("Invalid argument.");
	}

	return Number(x) + Number(y);
};

const subtractFractions = (x, y) => {
	const hasFractions = getIsFraction(x) || getIsFraction(y);
	if (hasFractions) {
		const { numerator: a, denominator: b } = splitFraction(x);
		const { numerator: c, denominator: d } = splitFraction(y);
		const numerator = (() => {
			const numerator1 = a * d;
			const numerator2 = c * b;
			return numerator1 - numerator2;
		})();
		const denominator = b * d;

		const result = reduceAndFormat(numerator, denominator);
		return result;
	}

	if (!getIsNumber(x) || !getIsNumber(y)) {
		throw new Error("Invalid argument.");
	}

	return Number(x) - Number(y);
};

const init = () => ({
	factors,
	primeFactors,
	gcd,
	lcm,
	mean,
	median,
	modes,
	pm,
	pmx,
	reduce,
	reduceAndFormat,
	rootReduce,
	getIsFraction,
	getIsNumber,
	splitFraction,
	divideFractions,
	multiplyFractions,
	rootReduceFraction,
	exponentiateFraction,
	squareFraction,
	addFractions,
	subtractFractions,
});

module.exports = init;
