const f = require("../format");
const seededRandom = require("../seededRandom");

const x = {
	random: seededRandom.seededRandom("12345"),
};
const format = f({ x });

describe("format", () => {
	describe("dd", () => {
		test("integer", async () => {
			const result = format.dd(1234);
			expect(result).toBe("1,234.00");
		});

		test("decimal", async () => {
			const result = format.dd(1234.5678);
			expect(result).toBe("1,234.57");
		});

		test("array", async () => {
			const result = format.dd([1234.5678]);
			expect(result).toEqual(["1,234.57"]);
		});
	});

	describe("dr", () => {
		test("integer", async () => {
			const result = format.dr(1234);
			expect(result).toBe("1,234");
		});

		test("decimal", async () => {
			const result = format.dr(1234.5678);
			expect(result).toBe("1,234.57");
		});

		test("array", async () => {
			const result = format.dr([1234.5678]);
			expect(result).toEqual(["1,234.57"]);
		});
	});

	describe("nf", () => {
		test("integer", async () => {
			const result = format.nf(1234, "0,0.0");
			expect(result).toBe("1,234.0");
		});

		test("array", async () => {
			const result = format.nf([1234.5678], "0,0.0");
			expect(result).toEqual(["1,234.6"]);
		});
	});
});
