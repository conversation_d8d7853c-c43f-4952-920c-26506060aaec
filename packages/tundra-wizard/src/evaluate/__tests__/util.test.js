const u = require("../util");
const constant = require("../constant")();
const seededRandom = require("../seededRandom");

const x = {
	random: seededRandom.seededRandom("12345"),
};
const util = u({ x });

describe("qca", () => {
	test("a", async () => {
		const result = util.qca(2, 1);
		expect(result).toBe("a");
	});

	test("b", async () => {
		const result = util.qca(1, 2);
		expect(result).toBe("b");
	});

	test("c", async () => {
		const result = util.qca(1, 1);
		expect(result).toBe("c");
	});
});

describe("qcs", () => {
	test("a", async () => {
		const result = util.qcs(2, 1);
		expect(result).toBe(">");
	});

	test("b", async () => {
		const result = util.qcs(1, 2);
		expect(result).toBe("<");
	});

	test("c", async () => {
		const result = util.qcs(1, 1);
		expect(result).toBe("=");
	});
});

describe("lighten", () => {
	test("default", async () => {
		const result = util.rgba(constant.chartColors.red);
		expect(result).toBe("rgba(255, 99, 132, 0.2)");
	});

	test("with alpha", async () => {
		const result = util.rgba(constant.chartColors.red, 0.5);
		expect(result).toBe("rgba(255, 99, 132, 0.5)");
	});
});

describe("escape", () => {
	test("escapes percents", async () => {
		const result = util.escape("1.02%");
		expect(result).toBe("1.02\\%");
	});

	test("escapes dollar signs", async () => {
		const result = util.escape("$1.02");
		expect(result).toBe("\\$1.02");
	});
});
