const m = require("../math");
const seededRandom = require("../seededRandom");

const x = {
	random: seededRandom.seededRandom("12345"),
};
const math = m({ x });

describe("gcd", () => {
	test("works", async () => {
		const result = math.gcd(24, 36);
		expect(result).toBe(12);
	});
});

describe("lcm", () => {
	test("works", async () => {
		const result = math.lcm(6, 15);
		expect(result).toBe(30);
	});
});

describe("factors", () => {
	test("works", async () => {
		const result = math.factors(12);
		expect(result).toEqual([1, 2, 3, 4, 6, 12]);
	});
});

describe("mean", () => {
	test("integer", async () => {
		const result = math.mean([1, 2, 3]);
		expect(result).toEqual(2);
	});

	test("decimal", async () => {
		const result = math.mean([1, 2]);
		expect(result).toEqual(1.5);
	});
});

describe("median", () => {
	test("odd", async () => {
		const result = math.median([1, 2, 3]);
		expect(result).toEqual(2);
	});

	test("even", async () => {
		const result = math.median([1, 2]);
		expect(result).toEqual(1.5);
	});
});

describe("modes", () => {
	test("single", async () => {
		const result = math.modes([1, 2, 3, 3]);
		expect(result).toEqual([3]);
	});

	test("multiple", async () => {
		const result = math.modes([1, 2, 2, 3, 3]);
		expect(result).toEqual([2, 3]);
	});
});

describe("reduce", () => {
	test("works", async () => {
		const result = math.reduce(4, 8);
		expect(result).toEqual([1, 2]);
	});
});

describe("pm", () => {
	test("positive", async () => {
		const result = math.pm(1);
		expect(result).toEqual("+1");
	});

	test("negative", async () => {
		const result = math.pm(-1);
		expect(result).toEqual(-1);
	});

	test("zero", async () => {
		const result = math.pm(0);
		expect(result).toEqual("+0");
	});
});

describe("pmx", () => {
	describe("with x", () => {
		test("positive x", async () => {
			const result = math.pmx(2, "y");
			expect(result).toEqual("+2y");
		});

		test("positive 1", async () => {
			const result = math.pmx(1, "y");
			expect(result).toEqual("+y");
		});

		test("negative x", async () => {
			const result = math.pmx(-2, "y");
			expect(result).toEqual("-2y");
		});

		test("negative 1", async () => {
			const result = math.pmx(-1, "y");
			expect(result).toEqual("-y");
		});

		test("zero x", async () => {
			const result = math.pmx(0, "y");
			expect(result).toEqual("");
		});
	});

	describe("without x", () => {
		test("positive", async () => {
			const result = math.pmx(2);
			expect(result).toEqual("+2");
		});

		test("positive 1", async () => {
			const result = math.pmx(1);
			expect(result).toEqual("+1");
		});

		test("negative", async () => {
			const result = math.pmx(-2);
			expect(result).toEqual(-2);
		});

		test("negative 1", async () => {
			const result = math.pmx(-1);
			expect(result).toEqual(-1);
		});

		test("zero", async () => {
			const result = math.pmx(0);
			expect(result).toEqual("");
		});
	});
});

describe("primeFactors", () => {
	test("1", async () => {
		const result = math.primeFactors(1);
		expect(result).toEqual([]); // 1 is a special case and has no prime factors
	});

	test("2", async () => {
		const result = math.primeFactors(2);
		expect(result).toEqual([2]);
	});

	test("3", async () => {
		const result = math.primeFactors(3);
		expect(result).toEqual([3]);
	});

	test("4", async () => {
		const result = math.primeFactors(4);
		expect(result).toEqual([2, 2]);
	});

	test("12", async () => {
		const result = math.primeFactors(12);
		expect(result).toEqual([2, 2, 3]);
	});

	test("30", async () => {
		const result = math.primeFactors(30);
		expect(result).toEqual([2, 3, 5]);
	});
});

describe("rootReduce", () => {
	test("1", async () => {
		const result = math.rootReduce(1);
		expect(result).toEqual({
			root: 1,
			radicand: null,
			markdown: "1",
		});
	});

	test("2", async () => {
		const result = math.rootReduce(2);
		expect(result).toEqual({
			root: null,
			radicand: 2,
			markdown: "\\sqrt{2}",
		});
	});

	test("3", async () => {
		const result = math.rootReduce(3);
		expect(result).toEqual({
			root: null,
			radicand: 3,
			markdown: "\\sqrt{3}",
		});
	});

	test("4", async () => {
		const result = math.rootReduce(4);
		expect(result).toEqual({
			root: 2,
			radicand: null,
			markdown: "2",
		});
	});

	test("6", async () => {
		const result = math.rootReduce(6);
		expect(result).toEqual({
			root: null,
			radicand: 6,
			markdown: "\\sqrt{6}",
		});
	});

	test("8", async () => {
		const result = math.rootReduce(8);
		expect(result).toEqual({
			root: 2,
			radicand: 2,
			markdown: "2\\sqrt{2}",
		});
	});

	test("12", async () => {
		const result = math.rootReduce(12);
		expect(result).toEqual({
			root: 2,
			radicand: 3,
			markdown: "2\\sqrt{3}",
		});
	});

	test("13", async () => {
		const result = math.rootReduce(13);
		expect(result).toEqual({
			root: null,
			radicand: 13,
			markdown: "\\sqrt{13}",
		});
	});

	test("16", async () => {
		const result = math.rootReduce(16);
		expect(result).toEqual({
			root: 4,
			radicand: null,
			markdown: "4",
		});
	});
});

describe("getIsFraction", () => {
	test("0", async () => {
		expect(math.getIsFraction(1)).toEqual(false);
		expect(math.getIsFraction("1")).toEqual(false);
	});

	test("1", async () => {
		expect(math.getIsFraction(1)).toEqual(false);
		expect(math.getIsFraction("1")).toEqual(false);
	});

	test("undefined", async () => {
		expect(math.getIsFraction(undefined)).toEqual(false);
	});

	test("null", async () => {
		expect(math.getIsFraction(null)).toEqual(false);
	});

	test("1/2", async () => {
		expect(math.getIsFraction("1/2")).toEqual(true);
		expect(math.getIsFraction(1 / 2)).toEqual(false);
	});

	test("0.5", async () => {
		expect(math.getIsFraction("0.5")).toEqual(false);
		expect(math.getIsFraction(0.5)).toEqual(false);
	});

	test("abcd", async () => {
		expect(math.getIsFraction("0.5")).toEqual(false);
	});

	test("(1/2)", async () => {
		expect(math.getIsFraction("(1/2)")).toEqual(false);
	});
});

describe("getIsNumber", () => {
	test("numbers", async () => {
		expect(math.getIsNumber(0)).toEqual(true);
		expect(math.getIsNumber(1)).toEqual(true);
		expect(math.getIsNumber(1.5)).toEqual(true);
		expect(math.getIsNumber(150 / 2)).toEqual(true);
	});

	test("strings", async () => {
		expect(math.getIsNumber("1")).toEqual(true);
		expect(math.getIsNumber("0.5")).toEqual(true);
		expect(math.getIsNumber("1/2")).toEqual(false);
		expect(math.getIsNumber("(1/2)")).toEqual(false);
		expect(math.getIsNumber("")).toEqual(false);
	});

	test("null / undefined", async () => {
		expect(math.getIsNumber(null)).toEqual(false);
		expect(math.getIsNumber(undefined)).toEqual(false);
	});
});

describe("spitFraction", () => {
	test("1", async () => {
		expect(math.splitFraction("1")).toEqual({ numerator: 1, denominator: 1 });
		expect(math.splitFraction(1)).toEqual({ numerator: 1, denominator: 1 });
	});

	test("0", async () => {
		expect(math.splitFraction("0")).toEqual({ numerator: 0, denominator: 1 });
		expect(math.splitFraction(0)).toEqual({ numerator: 0, denominator: 1 });
	});

	test("1/2", async () => {
		expect(math.splitFraction("1/2")).toEqual({ numerator: 1, denominator: 2 });
		expect(math.splitFraction(1 / 2)).toEqual({ numerator: 1, denominator: 2 });
	});

	test("12/13", async () => {
		expect(math.splitFraction("12/13")).toEqual({ numerator: 12, denominator: 13 });
	});

	test("undefined", async () => {
		expect(() => math.splitFraction(undefined)).toThrow(new Error("Invalid argument.")); //?
	});

	test("null", async () => {
		expect(() => math.splitFraction(null)).toThrow(new Error("Invalid argument.")); //?
	});

	test("(1/2)", async () => {
		expect(() => math.splitFraction("(1/2)")).toThrow(new Error("Invalid argument.")); //?
	});
});

describe("divideFractions", () => {
	test("1 divided by 2", async () => {
		expect(math.divideFractions("1", "2")).toEqual("1/2");
		expect(math.divideFractions(1, 2)).toEqual("1/2");
	});

	test("0.5 divided by 2", async () => {
		expect(math.divideFractions("0.5", "2")).toEqual("1/4");
		expect(math.divideFractions(0.5, 2)).toEqual("1/4");
	});

	test("1/2 divided by 2/3", async () => {
		expect(math.divideFractions("1/2", "2/3")).toEqual("3/4");
	});
	test("12/13 divided by 7/8", async () => {
		expect(math.divideFractions("12/13", "7/8")).toEqual("96/91");
	});

	test("division by zero", async () => {
		expect(() => math.divideFractions("1", "0")).toThrow("Cannot divide by zero.");
		expect(() => math.divideFractions(1, 0)).toThrow("Cannot divide by zero.");
		expect(() => math.divideFractions("1/0", 0)).toThrow("Cannot divide by zero.");
	});

	test("undefined", async () => {
		expect(() => math.divideFractions(undefined, undefined)).toThrow(new Error("Invalid argument.")); //?
		expect(() => math.divideFractions(1, undefined)).toThrow(new Error("Invalid argument.")); //?
		expect(() => math.divideFractions(undefined, 1)).toThrow(new Error("Invalid argument.")); //?
	});

	test("null", async () => {
		expect(() => math.divideFractions(null, null)).toThrow(new Error("Invalid argument.")); //?
		expect(() => math.divideFractions(1, null)).toThrow(new Error("Invalid argument.")); //?
		expect(() => math.divideFractions(null, 1)).toThrow(new Error("Invalid argument.")); //?
	});

	test("empty string", async () => {
		expect(() => math.divideFractions("", "")).toThrow(new Error("Invalid argument.")); //?
		expect(() => math.divideFractions(1, "")).toThrow(new Error("Invalid argument.")); //?
		expect(() => math.divideFractions("", 1)).toThrow(new Error("Invalid argument.")); //?
	});
});

describe("multiplyFractions", () => {
	test("1 multiplied", async () => {
		expect(math.multiplyFractions("1", "2")).toEqual(2);
		expect(math.multiplyFractions(1, 2)).toEqual(2);
		expect(math.multiplyFractions("1", "3/2")).toEqual("3/2");
		expect(math.multiplyFractions(1, 3 / 2)).toEqual("3/2");
	});

	test("0.5 multiplied", async () => {
		expect(math.multiplyFractions("0.5", "2")).toEqual(1);
		expect(math.multiplyFractions(0.5, 2)).toEqual(1);
		expect(math.multiplyFractions("0.5", "3")).toEqual("3/2");
	});

	test("1/2 multiplied by 2/3", async () => {
		expect(math.multiplyFractions("1/2", "2/3")).toEqual("1/3");
	});

	test("12/13 multiplied by 7/8", async () => {
		expect(math.multiplyFractions("12/13", "7/8")).toEqual("21/26");
	});

	test("undefined", async () => {
		expect(() => math.multiplyFractions(undefined, undefined)).toThrow(new Error("Invalid argument.")); //?
		expect(() => math.multiplyFractions(1, undefined)).toThrow(new Error("Invalid argument.")); //?
		expect(() => math.multiplyFractions(undefined, 1)).toThrow(new Error("Invalid argument.")); //?
	});

	test("null", async () => {
		expect(() => math.multiplyFractions(null, null)).toThrow(new Error("Invalid argument.")); //?
		expect(() => math.multiplyFractions(1, null)).toThrow(new Error("Invalid argument.")); //?
		expect(() => math.multiplyFractions(null, 1)).toThrow(new Error("Invalid argument.")); //?
	});

	test("empty string", async () => {
		expect(() => math.multiplyFractions("", "")).toThrow(new Error("Invalid argument.")); //?
		expect(() => math.multiplyFractions(1, "")).toThrow(new Error("Invalid argument.")); //?
		expect(() => math.multiplyFractions("", 1)).toThrow(new Error("Invalid argument.")); //?
	});
});

describe("rootReduceFraction", () => {
	test("1", async () => {
		expect(math.rootReduceFraction(1)).toEqual("1");
		expect(math.rootReduceFraction("1")).toEqual("1");
	});

	test("4", async () => {
		expect(math.rootReduceFraction(4)).toEqual("2");
		expect(math.rootReduceFraction("4")).toEqual("2");
	});

	test("1/2", async () => {
		expect(math.rootReduceFraction("1/2")).toEqual("1/\\sqrt{2}");
	});

	test("4/16", async () => {
		expect(math.rootReduceFraction("4/16")).toEqual("1/2");
	});

	test("27/99", async () => {
		const result = math.rootReduceFraction("27/99"); //?
		expect(result).toEqual("3\\sqrt{3}/3\\sqrt{11}");
	});

	test("undefined", async () => {
		expect(() => math.rootReduceFraction(undefined)).toThrow(new Error("Invalid argument.")); //?
	});

	test("null", async () => {
		expect(() => math.rootReduceFraction(null)).toThrow(new Error("Invalid argument.")); //?
	});

	test("empty string", async () => {
		expect(math.rootReduceFraction("")).toEqual(0);
	});
});

describe("exponentiateFraction", () => {
	test("1", async () => {
		expect(math.exponentiateFraction(1)).toEqual(1);
		expect(math.exponentiateFraction("1")).toEqual(1);
	});

	test("4", async () => {
		expect(math.exponentiateFraction(4)).toEqual(4);
		expect(math.exponentiateFraction("4")).toEqual(4);
		expect(math.exponentiateFraction(4, 2)).toEqual(16);
		expect(math.exponentiateFraction("4", 2)).toEqual(16);
		expect(math.exponentiateFraction("4", "2")).toEqual(16);
		expect(math.exponentiateFraction(4, "2")).toEqual(16);
	});

	test(0.5, async () => {
		expect(math.exponentiateFraction(0.5)).toEqual(0.5);
		expect(math.exponentiateFraction(0.5, 2)).toEqual(0.25);
		expect(math.exponentiateFraction(0.5, 3)).toEqual(0.125);
	});

	test("1/2", async () => {
		expect(math.exponentiateFraction("1/2")).toEqual("1/2");
		expect(math.exponentiateFraction("1/2", 2)).toEqual("1/4");
		expect(math.exponentiateFraction("1/2", 3)).toEqual("1/8");
	});

	test("3/4", async () => {
		expect(math.exponentiateFraction("3/4")).toEqual("3/4");
		expect(math.exponentiateFraction("3/4", 2)).toEqual("9/16");
		expect(math.exponentiateFraction("3/4", 3)).toEqual("27/64");
	});

	test("27/99", async () => {
		expect(math.exponentiateFraction("27/99")).toEqual("3/11");
		expect(math.exponentiateFraction("27/99", 2)).toEqual("9/121");
	});

	test("undefined", async () => {
		expect(() => math.exponentiateFraction(undefined)).toThrow(new Error("Invalid argument.")); //?
	});

	test("null", async () => {
		expect(() => math.exponentiateFraction(null)).toThrow(new Error("Invalid argument.")); //?
	});

	test("empty string", async () => {
		expect(() => math.exponentiateFraction("")).toThrow(new Error("Invalid argument.")); //?
	});
});

describe("addFractions", () => {
	test("1", async () => {
		expect(() => math.addFractions(1)).toThrow();
		expect(math.addFractions("1", 0)).toEqual(1);
		expect(math.addFractions(1, 0)).toEqual(1);
		expect(math.addFractions(1, 1)).toEqual(2);
	});

	test(0.5, async () => {
		expect(() => math.addFractions(0.5)).toThrow();
		expect(math.addFractions(0.5, 2)).toEqual(2.5);
		expect(math.addFractions(0.5, 3)).toEqual(3.5);
		expect(math.addFractions(0.5, "1/2")).toEqual(1);
	});

	test("1/2", async () => {
		expect(() => math.addFractions("1/2")).toThrow();
		expect(math.addFractions("1/2", 2)).toEqual("5/2");
		expect(math.addFractions("1/2", 3)).toEqual("7/2");
		expect(math.addFractions("1/2", "3/4")).toEqual("5/4");
	});

	test("3/4", async () => {
		expect(() => math.addFractions("3/4")).toThrow();
		expect(math.addFractions("3/4", 2)).toEqual("11/4");
		expect(math.addFractions("3/4", "7/12")).toEqual("4/3");
	});

	test("27/99", async () => {
		expect(() => math.addFractions("27/99")).toThrow();
		expect(math.addFractions("27/99", 2)).toEqual("25/11");
		expect(math.addFractions("27/99", "97/103")).toEqual("1376/1133");
	});

	test("undefined", async () => {
		expect(() => math.addFractions(undefined)).toThrow(new Error("Invalid argument.")); //?
	});

	test("null", async () => {
		expect(() => math.addFractions(null)).toThrow(new Error("Invalid argument.")); //?
	});

	test("empty string", async () => {
		expect(() => math.addFractions("")).toThrow(new Error("Invalid argument.")); //?
	});
});

describe("subtractFractions", () => {
	test("1", async () => {
		expect(() => math.subtractFractions(1)).toThrow();
		expect(math.subtractFractions("1", 0)).toEqual(1);
		expect(math.subtractFractions(1, 0)).toEqual(1);
		expect(math.subtractFractions(1, 1)).toEqual(0);
	});

	test(0.5, async () => {
		expect(() => math.subtractFractions(0.5)).toThrow();
		expect(math.subtractFractions(0.5, 2)).toEqual(-1.5);
		expect(math.subtractFractions(0.5, 3)).toEqual(-2.5);
		expect(math.subtractFractions(0.5, "1/2")).toEqual(0);
	});

	test("1/2", async () => {
		expect(() => math.subtractFractions("1/2")).toThrow();
		expect(math.subtractFractions("1/2", 2)).toEqual("-3/2");
		expect(math.subtractFractions("1/2", 3)).toEqual("-5/2");
		expect(math.subtractFractions("1/2", "3/4")).toEqual("-1/4");
	});

	test("3/4", async () => {
		expect(() => math.subtractFractions("3/4")).toThrow();
		expect(math.subtractFractions("3/4", 2)).toEqual("-5/4");
		expect(math.subtractFractions("3/4", "7/12")).toEqual("1/6");
	});

	test("27/99", async () => {
		expect(() => math.subtractFractions("27/99")).toThrow();
		expect(math.subtractFractions("27/99", 2)).toEqual("-19/11");
		expect(math.subtractFractions("27/99", "97/103")).toEqual("-758/1133");
	});

	test("undefined", async () => {
		expect(() => math.subtractFractions(undefined)).toThrow(new Error("Invalid argument.")); //?
	});

	test("null", async () => {
		expect(() => math.subtractFractions(null)).toThrow(new Error("Invalid argument.")); //?
	});

	test("empty string", async () => {
		expect(() => math.subtractFractions("")).toThrow(new Error("Invalid argument.")); //?
	});
});
