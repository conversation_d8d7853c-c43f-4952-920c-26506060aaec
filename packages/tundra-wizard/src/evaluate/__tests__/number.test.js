const n = require("../number");
const seededRandom = require("../seededRandom");

const x = {
	random: seededRandom.seededRandom("12345"),
};
const number = n({ x });

describe("round", () => {
	test("works", async () => {
		const r1 = number.r(0.5, 2);
		expect(r1).toBe(0.5);

		const r2 = number.r(0.499, 2);
		expect(r2).toBe(0.5);

		const r3 = number.r(0.501, 2);
		expect(r3).toBe(0.5);

		const r4 = number.r(0.505, 2);
		expect(r4).toBe(0.51);
	});

	// 20250518JP: Not mathematically correct, but this handles floating point errors.
	test("handles floating point errors", async () => {
		const r1 = number.r(0.5049, 2);
		expect(r1).toBe(0.5);

		const r2 = number.r(0.50499, 3);
		expect(r2).toBe(0.505);

		const r3 = number.r(0.50499, 2);
		expect(r3).toBe(0.51);

		const r4 = number.r(15447.795, 2);
		expect(r4).toBe(15447.8);

		const r5 = number.r(15447.79499999, 2);
		expect(r5).toBe(15447.8);

		const r6 = number.r(15447.79499999, 3);
		expect(r6).toBe(15447.795);

		const r7 = number.r(15447.79499999, 4);
		expect(r7).toBe(15447.795);
	});

	test("handles strings accidentally passed", async () => {
		const r1 = number.r("0.5049", 2);
		expect(r1).toBe(0.5);

		const r2 = number.r("0.50499", 3);
		expect(r2).toBe(0.505);

		const r3 = number.r("0.50499", 2);
		expect(r3).toBe(0.51);

		const r4 = number.r("15447.795", 2);
		expect(r4).toBe(15447.8);

		const r5 = number.r("15447.79499999", 2);
		expect(r5).toBe(15447.8);

		const r6 = number.r("15447.79499999", 3);
		expect(r6).toBe(15447.795);

		const r7 = number.r("15447.79499999", 4);
		expect(r7).toBe(15447.795);
	});
});
