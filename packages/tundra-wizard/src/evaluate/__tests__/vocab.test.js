const v = require("../vocab");
const seededRandom = require("../seededRandom");

const x = {
	random: seededRandom.seededRandom("12345"),
};
const vocab = v({ x });

const bundle = {
	word: "enervate",
	sentences: [
		"Not getting enough sleep would enervate just about anyone.",
		"The wrestler's plan was to slowly wear out his opponent in order to enervate him.",
	],
	synonym: "exhaust",
	definition: {
		partOfSpeech: "transitive verb",
		text: "To deprive of nerve, force, strength, or courage; to render feeble or impotent; to make effeminate; to impair the moral powers of.",
		attributionText: "from the GNU version of the Collaborative International Dictionary of English.",
		attributionUrl: "http://gcide.gnu.org.ua/",
		sourceDictionary: "gcide",
	},
};

const exact = [
	{
		word: "mitigate",
		definition:
			"To make less severe, intense, harsh, rigorous, painful, etc.; to soften; to meliorate; to alleviate; to diminish; to lessen",
	},
	{
		word: "precipitate",
		definition:
			"To urge or press on with eager haste or violence; to cause to happen, or come to a crisis, suddenly or too soon.",
	},
];

const grouped = [
	{
		word: "assuage",
		definition: "To lessen the intensity of, to mitigate or relieve (hunger, emotion, pain etc.).",
	},
	{ word: "bolster", definition: "To brace, reinforce, secure, or support." },
	{
		word: "prevaricate",
		definition: "be deliberately ambiguous or unclear in order to mislead or withhold information",
	},
];

const other = [
	{
		word: "equivocal",
		definition: "A word or expression capable of different meanings; an ambiguous term; an equivoque.",
	},
	{
		word: "banal",
		definition: "Common in a boring way, to the point of being predictable; containing nothing new or fresh.",
	},
	{ word: "innocuous", definition: "Harmless; producing no ill effect." },
	{
		word: "capricious",
		definition: "Governed or characterized by caprice; apt to change suddenly; freakish; whimsical; changeable.",
	},
	{ word: "alacrity", definition: "liveliness and eagerness" },
	{
		word: "lucid",
		definition: "(of language) transparently clear; easily understandable",
	},
	{ word: "insipid", definition: "lacking interest or significance or impact" },
	{
		word: "loquacious",
		definition: "Given to continual talking; talkative; garrulous.",
	},
	{ word: "erudite", definition: "having or showing profound knowledge" },
	{ word: "quotidian", definition: "found in the ordinary course of events" },
	{ word: "laudable", definition: "worthy of high praise" },
	{
		word: "audacious",
		definition: "Showing willingness to take bold risks; recklessly daring.",
	},
	{
		word: "ephemeral",
		definition: "Short-lived; existing or continuing for a short time only.",
	},
	{ word: "malleable", definition: "Flexible, liable to change." },
	{ word: "ingenuous", definition: "Naive and trusting." },
	{
		word: "prodigal",
		definition:
			"Given to extravagant expenditure; expending money or other things without necessity; recklessly or viciously profuse; lavish; wasteful; not frugal or economical",
	},
	{
		word: "laconic",
		definition:
			"Expressing much in few words, after the manner of the Laconians or Spartans; brief and pithy; concise; brusque; epigrammatic. In this sense laconic is the usual form.",
	},
	{
		word: "apathy",
		definition: "the trait of lacking enthusiasm for or interest in things generally",
	},
	{
		word: "obdurate",
		definition: "Stubbornly persistent, generally in wrongdoing; refusing to reform or repent.",
	},
];

describe("vocabQuiz", () => {
	test("works", async () => {
		const { prompt, answer, distractors, explanation } = vocab.getQuiz({
			bundle,
			distractors: {
				exact,
				grouped,
				other,
			},
		}); //?

		expect(prompt).toEqual(expect.stringContaining("Select the word"));

		expect(answer).toBe("enervate");

		const allSelectedDistractors = [...grouped, ...other, ...exact].map(
			(selectedDistractor) => selectedDistractor.word
		);
		expect(Array.isArray(distractors)).toBeTruthy();
		expect(allSelectedDistractors).toEqual(expect.arrayContaining(distractors));

		expect(explanation).toEqual(expect.stringContaining('The correct answer is "**enervate**".'));
		expect(explanation).toEqual(expect.stringContaining(`<Aaa.VocabWord bundle={${JSON.stringify(bundle)}}/>`));
	});
});
