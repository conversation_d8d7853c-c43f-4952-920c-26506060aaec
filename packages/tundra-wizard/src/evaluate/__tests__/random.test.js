const r = require("../random");
const seededRandom = require("../seededRandom");

describe("intInclusive", () => {
	const x = {
		random: seededRandom.seededRandom("12345"),
	};
	const random = r({ x });

	test("is consistent with a seed", async () => {
		const result1 = random.ii(1, 19); //?
		const result2 = random.ii(1, 19); //?
		const result3 = random.ii(1, 19); //?
		expect(result1).toEqual(9);
		expect(result2).toEqual(1);
		expect(result3).toEqual(18);
	});
});

describe("evenIntInclusive", () => {
	const x = {
		random: seededRandom.seededRandom("12345"),
	};
	const random = r({ x });

	test("it works", async () => {
		const result1 = random.evenIntInclusive(1, 3);
		const result2 = random.evenIntInclusive(1, 3);
		const result3 = random.evenIntInclusive(1, 3);
		const result4 = random.evenIntInclusive(2, 4);
		const result5 = random.evenIntInclusive(2, 4);
		const result6 = random.evenIntInclusive(2, 4);
		const result7 = random.evenIntInclusive(-3, -1);
		const result8 = random.evenIntInclusive(-3, -1);
		const result9 = random.evenIntInclusive(-3, -1);
		const result10 = random.evenIntInclusive(-4, -2);
		const result11 = random.evenIntInclusive(-4, -2);
		const result12 = random.evenIntInclusive(-4, -2);
		const result13 = random.evenIntInclusive(-1, 1);
		const result14 = random.evenIntInclusive(-1, 1);
		const result15 = random.evenIntInclusive(-1, 1);

		expect(result1).toEqual(2);
		expect(result2).toEqual(2);
		expect(result3).toEqual(2);
		expect(result4).toEqual(2);
		expect(result5).toEqual(4);
		expect(result6).toEqual(2);
		expect(result7).toEqual(-2);
		expect(result8).toEqual(-2);
		expect(result9).toEqual(-2);
		expect(result10).toEqual(-2);
		expect(result11).toEqual(-4);
		expect(result12).toEqual(-2);
		expect(result13).toEqual(0);
		expect(result14).toEqual(0);
		expect(result15).toEqual(0);
	});
});

describe("oddIntInclusive", () => {
	const x = {
		random: seededRandom.seededRandom("12345"),
	};
	const random = r({ x });

	test("it works", async () => {
		const result1 = random.oddIntInclusive(1, 3);
		const result2 = random.oddIntInclusive(1, 3);
		const result3 = random.oddIntInclusive(1, 3);
		const result4 = random.oddIntInclusive(2, 4);
		const result5 = random.oddIntInclusive(2, 4);
		const result6 = random.oddIntInclusive(2, 4);
		const result7 = random.oddIntInclusive(-3, -1);
		const result8 = random.oddIntInclusive(-3, -1);
		const result9 = random.oddIntInclusive(-3, -1);
		const result10 = random.oddIntInclusive(-4, -2);
		const result11 = random.oddIntInclusive(-4, -2);
		const result12 = random.oddIntInclusive(-4, -2);
		const result13 = random.oddIntInclusive(-1, 1);
		const result14 = random.oddIntInclusive(-1, 1);
		const result15 = random.oddIntInclusive(-1, 1);

		expect(result1).toEqual(1);
		expect(result1).toEqual(1);
		expect(result3).toEqual(1);
		expect(result4).toEqual(3);
		expect(result5).toEqual(3);
		expect(result6).toEqual(3);
		expect(result7).toEqual(-3);
		expect(result8).toEqual(-1);
		expect(result9).toEqual(-3);
		expect(result10).toEqual(-3);
		expect(result11).toEqual(-3);
		expect(result12).toEqual(-3);
		expect(result13).toEqual(1);
		expect(result14).toEqual(-1);
		expect(result15).toEqual(1);
	});
});

describe("sampleTiered", () => {
	const x = {
		random: seededRandom.seededRandom("12345"),
	};
	const random = r({ x });

	it("works", () => {
		const array1 = [1, 2];
		const array2 = [3, 4];
		const array3 = [5];
		const array4 = [7, 8, 9, 10, 11];
		const array5 = [12];
		const array6 = [13, 14, 15, 16];

		const result1 = random.sampleTiered([array1, array2, array3], 5);
		const result2 = random.sampleTiered([array4, array1, array2], 5);
		const result3 = random.sampleTiered([array5, array6, array2], 5);

		expect(result1).toEqual(expect.arrayContaining([1, 2, 3, 4, 5]));
		expect(result1.length).toBe(5);
		expect(result2).toEqual(expect.arrayContaining([7, 8, 9, 10, 11]));
		expect(result2.length).toBe(5);
		expect(result3).toEqual(expect.arrayContaining([12, 13, 14, 15, 16]));
		expect(result3.length).toBe(5);
	});
});

describe("sampleGrouped", () => {
	const x = {
		random: seededRandom.seededRandom("12345"),
	};
	const random = r({ x });

	it("throw an error for bad input", () => {
		expect(() => random.sampleGrouped(3, 3)).toThrow("Expected an array as the first argument");

		expect(() => random.sampleGrouped(null, 3)).toThrow("Expected an array as the first argument");

		expect(() => random.sampleGrouped(undefined, 3)).toThrow("Expected an array as the first argument");

		expect(() => random.sampleGrouped(["1.a", ["2.a"], [3]], 3)).toThrow(
			"Expected an array of arrays as the first argument"
		);
	});

	it("works", () => {
		const result1 = random.sampleGrouped([["1.a"], ["2.a"], ["3"]], 5); //?
		const result2 = random.sampleGrouped([["1.a", "1.b"], ["2.a", "2.b"], ["3"]], 3); //?
		const result3 = random.sampleGrouped(
			[
				["1.a", "1.b"],
				["2.a", "2.b"],
				["3.a", "3.b"],
				["cat", "dog"],
				[1, 2],
			],
			3
		); //?
		const result4 = random.sampleGrouped([["1.a", "1.b"], ["2.a", "2.b"], ["3"], ["cat", "dog"], [2]], 5); //?
		const result5 = random.sampleGrouped([[1], [2], []], 3); //?
		const result6 = random.sampleGrouped([[null], [undefined], [1, 2], ["cat", "dog"]], 4); //?

		expect(result1.length).toBe(3);
		expect(result1).toEqual(expect.arrayContaining(["1.a", "2.a", "3"]));

		expect(result2.length).toBe(3);
		expect(result2).toEqual(expect.arrayContaining(["3"]));

		expect(result3.length).toBe(3);

		expect(result4.length).toBe(5);
		expect(result4).toEqual(expect.arrayContaining([2, "3"]));

		expect(result5.length).toBe(2);
		expect(result5).toEqual(expect.arrayContaining([1, 2]));

		expect(result6.length).toBe(4);
		expect(result6).toEqual(expect.arrayContaining([null, undefined]));
	});
});

// test('seeds sample', async () => {
//   const result1 = random.sample([1, 2, 3, 4, 5, 6, 7, 8, 9]);
//   const result2 = random.sample([1, 2, 3, 4, 5, 6, 7, 8, 9]);
//   const result3 = random.sample([1, 2, 3, 4, 5, 6, 7, 8, 9]);
//   expect(result1).toEqual(5);
//   expect(result2).toEqual(1);
//   expect(result3).toEqual(9);
// });

// test('seeds sampleSize', async () => {
//   const result1 = random.sampleSize([1, 2, 3, 4, 5, 6, 7, 8, 9], 2);
//   const result2 = random.sampleSize([1, 2, 3, 4, 5, 6, 7, 8, 9], 2);
//   const result3 = random.sampleSize([1, 2, 3, 4, 5, 6, 7, 8, 9], 2);
//   expect(result1).toEqual([5, 2]);
//   expect(result2).toEqual([9, 4]);
//   expect(result3).toEqual([4, 1]);
// });

// test('seeds shuffle', async () => {
//   const result1 = random.shuffle([1, 2, 3, 4, 5, 6, 7, 8, 9], 2);
//   const result2 = random.shuffle([1, 2, 3, 4, 5, 6, 7, 8, 9], 2);
//   const result3 = random.shuffle([1, 2, 3, 4, 5, 6, 7, 8, 9], 2);
//   expect(result1).toEqual([5, 2, 9, 1, 6, 7, 3, 8, 4]);
//   expect(result2).toEqual([1, 8, 3, 2, 7, 9, 4, 6, 5]);
//   expect(result3).toEqual([6, 8, 4, 7, 2, 3, 1, 5, 9]);
// });

// test('seeds sign', async () => {
//   const result1 = random.sign();
//   const result2 = random.sign();
//   const result3 = random.sign();
//   expect(result1).toEqual(-1);
//   expect(result2).toEqual(-1);
//   expect(result3).toEqual(1);
// });
