const g = require("../geometry");
const seededRandom = require("../seededRandom");

describe("generateTriangleFromPerimeter", () => {
	test("works", async () => {
		const x1 = { random: seededRandom.seededRandom("12345") };
		const x2 = { random: seededRandom.seededRandom("12346") };
		const x3 = { random: seededRandom.seededRandom("12347") };

		const result1 = g({ x: x1 }).generateTriangleFromPerimeter(12); //?
		expect(result1).toEqual([3, 4, 5]);

		const result2 = g({ x: x2 }).generateTriangleFromPerimeter(12); //?
		expect(result2).toEqual([4, 5, 3]);

		const result3 = g({ x: x3 }).generateTriangleFromPerimeter(12); //?
		expect(result3).toEqual([2, 5, 5]);
	});
});
