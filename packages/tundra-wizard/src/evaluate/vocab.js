const lodash = require("lodash");
const r = require("./random");

const init = ({ x }) => {
	const random = r({ x });
	const _ = lodash.runInContext();

	const getFormattedDistractors = ({ distractors }) => {
		const formattedDistractors = distractors
			.map((distractor) => `- **${distractor.word}**: ${distractor.definition}`)
			.join("\n");
		return formattedDistractors;
	};

	const getVocabWordComponent = ({ bundle }) => {
		const component = `<Aaa.VocabWord bundle={${JSON.stringify(bundle)}}/>`;
		return component;
	};

	const getExplanation = ({ bundle, selectedDistractors }) => {
		const markdown = getVocabWordComponent({ bundle });

		const sortedDistractors = selectedDistractors.sort((a, b) => {
			if (a.word > b.word) return 1;
			if (a.word < b.word) return -1;

			return 0;
		});
		const formattedDistractors = getFormattedDistractors({
			distractors: sortedDistractors,
		});

		const { word } = bundle;
		const explanation = `The correct answer is "**${word}**".

${markdown}

Meanings for the other words:

${formattedDistractors}`;

		return explanation;
	};

	const getSelectedDistractors = ({ distractors, count = 5 }) => {
		const { exact = [], grouped = [], other = [] } = distractors;
		const selectedDistractors = random.sampleTiered([exact, grouped, other], count);
		return selectedDistractors;
	};

	const getPhraseClozeDeletion = ({ sentences, word }) => {
		const regex = new RegExp(word, "i");
		const randomSentence = _.sample(sentences);
		const phraseClozeDeletion = randomSentence.replace(regex, "______");

		return phraseClozeDeletion;
	};

	const getClozeDeletionQuiz = ({ bundle, distractors }) => {
		const { sentences, word } = bundle;
		const phraseClozeDeletion = getPhraseClozeDeletion({ sentences, word });
		const prompt = `Select the word missing from this sentence: \n\n*"${phraseClozeDeletion}"*`;
		const answer = word;

		const selectedDistractors = getSelectedDistractors({ distractors });
		const explanation = getExplanation({ bundle, selectedDistractors });
		const distractorWords = selectedDistractors.map((distractor) => distractor.word);

		return { prompt, answer, explanation, distractors: distractorWords };
	};

	const getDefinitionQuiz = ({ bundle, distractors }) => {
		const { definition, word } = bundle;
		const { text } = definition;
		const prompt = `Select the word that matches this definition: \n\n_"${text}"_`;
		const answer = word;

		const selectedDistractors = getSelectedDistractors({ distractors });
		const explanation = getExplanation({ bundle, selectedDistractors });
		const distractorWords = selectedDistractors.map((distractor) => distractor.word);

		return { prompt, answer, explanation, distractors: distractorWords };
	};

	const getSynonymQuiz = ({ bundle, distractors }) => {
		const { synonym, word } = bundle;
		const prompt = `Select the word closest in meaning to *"${synonym}"*;`;
		const answer = word;

		const selectedDistractors = getSelectedDistractors({ distractors });
		const explanation = getExplanation({ bundle, selectedDistractors });
		const distractorWords = selectedDistractors.map((distractor) => distractor.word);

		return { prompt, answer, explanation, distractors: distractorWords };
	};

	const getQuiz = ({ bundle, distractors }) => {
		const quizFn = _.sample([getClozeDeletionQuiz, getDefinitionQuiz, getSynonymQuiz]);
		const quiz = quizFn({ bundle, distractors });

		return quiz;
	};

	return { getQuiz };
};

module.exports = init;
