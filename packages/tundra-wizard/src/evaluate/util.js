const { Base64 } = require("js-base64");
const r = require("./random");

const { STENCIL_BASE_URL } = process.env;

const init = ({ x }) => {
	const random = r({ x });

	const quantityComparisonAnswer = (a, b) => {
		if (a > b) return "a";
		if (a < b) return "b";
		return "c";
	};

	const quantityComparisonSign = (a, b) => {
		if (a > b) return ">";
		if (a < b) return "<";
		return "=";
	};

	const createChoicesRange = ({ answer, step, num = 6, condition = () => true, f = (x) => x }) => {
		const choices = [];

		const before = random.ii(0, num - 1);
		const after = num - 1 - before;

		for (let i = 0; i < before; i += 1) {
			const val = answer - step * (i + 1);
			if (condition(val)) {
				choices.unshift({
					markdown: f(val),
				});
			}
		}
		if (condition(answer)) {
			// 20200514JP: This still applies the condition because we want to fail bundle validation
			//   if we've accidentally generated an invalid answer.
			choices.push({
				markdown: f(answer),
				correct: true,
			});
		}
		for (let i = 0; i < after; i += 1) {
			const val = answer + step * (i + 1);
			if (condition(val)) {
				choices.push({
					markdown: f(val),
				});
			}
		}
		return choices;
	};

	const createChoicesRangeMultiple = ({ anchor, step, num = 6, correct, f = (x) => x }) => {
		const choices = [];

		const before = random.ii(0, num - 1);
		const after = num - 1 - before;

		for (let i = 0; i < before; i += 1) {
			const val = anchor - step * (i + 1);
			choices.unshift({
				markdown: f(val),
				correct: correct(val),
			});
		}
		choices.push({
			markdown: f(anchor),
			correct: correct(anchor),
		});
		for (let i = 0; i < after; i += 1) {
			const val = anchor + step * (i + 1);
			choices.push({
				markdown: f(val),
				correct: correct(val),
			});
		}
		return choices;
	};

	const RGB_COLOR_REGEX = /rgb\((\d+),\s*(\d+),\s*(\d+)\s*\)/i;
	const rgba = (color, alpha = 0.2) => {
		const matches = RGB_COLOR_REGEX.exec(color);
		const [, r, g, b] = matches;

		return `rgba(${r}, ${g}, ${b}, ${alpha})`;
	};

	const getStencilHref = ({ stencil, data, json }) => {
		const obj =
			json ||
			JSON.stringify({
				s: stencil,
				d: data,
			});
		const encoded = Base64.encode(obj);
		return `${STENCIL_BASE_URL || "https://stencil.achievable.me"}/x/${encoded}`;
	};

	const stencil = ({ stencil, data, classes }) => {
		const stencilHref = getStencilHref({ stencil, data });

		const markdown = `
::: class ${classes}
![figure](${stencilHref})
:::
  `;

		return markdown;
	};

	const escape = (input) => {
		let output = input;
		output = output.replace(/%/g, "\\%");
		output = output.replace(/\$/g, "\\$");
		return output;
	};

	class RandomizeError extends Error {
		constructor(message) {
			super(message);
			this.name = this.constructor.name;
			this.code = "RANDOMIZE_ERROR";
			Error.captureStackTrace(this, this.constructor);
		}
	}

	const throwRandomizeError = (message) => {
		throw new RandomizeError(message);
	};

	return {
		createChoicesRange,
		createChoicesRangeMultiple,
		getStencilHref,
		qca: quantityComparisonAnswer,
		qcs: quantityComparisonSign,
		rgba,
		stencil,
		escape,
		RandomizeError,
		throwRandomizeError,
	};
};

module.exports = init;
