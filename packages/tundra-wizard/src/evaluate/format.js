const _ = require("lodash");
const numeral = require("numeral");

const addOrdinalSuffix = (i, opts = {}) => {
	const { isKatex = false } = opts;
	const j = i % 10;
	const k = i % 100;

	const result = (() => {
		if (j == 1 && k != 11) {
			return i + "st";
		}

		if (j == 2 && k != 12) {
			return i + "nd";
		}

		if (j == 3 && k != 13) {
			return i + "rd";
		}

		return i + "th";
	})();

	if (isKatex) {
		return `\\text{${result}}`;
	}

	return result;
};

const typeWrapper = (obj, fn) => {
	if (_.isArray(obj)) {
		return obj.map((val) => {
			return fn(val);
		});
	}

	return fn(obj);
};

const numeralFormat = (obj, format) => {
	return typeWrapper(obj, (val) => {
		return numeral(val).format(format);
	});
};

const currencyDecimal = (obj) => {
	return typeWrapper(obj, (val) => {
		return numeral(val).format("$0,0.00");
	});
};

const currencyInteger = (obj) => {
	return typeWrapper(obj, (val) => {
		return numeral(val).format("$0,0");
	});
};

const decimal = (obj) => {
	return typeWrapper(obj, (val) => {
		return numeral(val).format("0,0.00");
	});
};

const decimalRound = (obj) => {
	return typeWrapper(obj, (val) => {
		return numeral(val).format("0,0.[00]");
	});
};

const percentDecimcal = (obj) => {
	return typeWrapper(obj, (val) => {
		return numeral(val).format("0,0.00%");
	});
};

const percentInteger = (obj) => {
	return typeWrapper(obj, (val) => {
		return numeral(val).format("0,0%");
	});
};

const integer = (obj) => {
	return typeWrapper(obj, (val) => {
		return numeral(val).format("0,0");
	});
};

const suffixOrdinal = (obj, opts) => {
	return typeWrapper(obj, (val) => {
		return addOrdinalSuffix(val, opts);
	});
};

const init = () => ({
	cd: currencyDecimal,
	ci: currencyInteger,
	dd: decimal,
	dr: decimalRound,
	ii: integer,
	nf: numeralFormat,
	pd: percentDecimcal,
	pi: percentInteger,
	so: suffixOrdinal,
});

module.exports = init;
