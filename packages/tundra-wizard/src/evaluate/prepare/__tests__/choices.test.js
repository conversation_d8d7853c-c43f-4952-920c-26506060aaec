const _ = require("lodash");
const {
	generateChoicesPassageChooseSentence,
	sortChoices,
	sortOrShuffleMultipleChoices,
	sortMultipleChoices,
} = require("../choices");
const bundlePassageChooseSentence = require("../__mocks__/bundlePassageChooseSentence");
const bundleMultipleChoice = require("../__mocks__/bundleMultipleChoice");

describe("generateChoicesPassageChooseSentence", () => {
	test("works", async () => {
		const result = generateChoicesPassageChooseSentence({
			bundle: bundlePassageChooseSentence,
		}); //?
		expect(result).toEqual([
			{
				uuid: expect.any(String),
				markdown:
					"Climate change and local anthropogenic stressors are causing unprecedented damage to coral reefs globally, necessitating novel techniques to counteract degradation and proactively manage these rapidly changing ecosystems.",
				correct: false,
			},
			{
				uuid: expect.any(String),
				markdown:
					" Taking active steps to maintain healthy fish communities will be vital in reversing reef degradation.",
				correct: false,
			},
			{
				uuid: expect.any(String),
				markdown:
					" Fishes perform a diverse suite of important functional processes, meaning that damaged reefs having a higher chance of recovery if they have healthy fish populations.",
				correct: false,
			},
			{
				uuid: expect.any(String),
				markdown:
					" Reef fish populations are sustained by recruitment, whereby young fish that spend their larval stage in the open ocean use a range of sensory cures to detect, orient toward, and settle to reef habitat.",
				correct: false,
			},
			{
				uuid: expect.any(String),
				markdown: "\n\n\n\n",
				correct: false,
			},
			{
				uuid: expect.any(String),
				markdown:
					"However, degraded reefs smell and sound less attractive to juvenile fishes, and receive lower levels of fish settlement than healthy systems.",
				correct: false,
			},
			{
				uuid: expect.any(String),
				markdown:
					" Artificially reversing degradation-associated sensory changes might restore habitat attractiveness, promote the settlement and retention of functionally important fish species, and enhance local-scale recovery processes.",
				correct: true,
			},
			{
				uuid: expect.any(String),
				markdown:
					" Acoustic cues are particularly amenable to artificial restoration, due to their use by a wide range of settlement-stage fishes and their ease of manipulation in field conditions using underwater loudspeakers.",
				correct: false,
			},
			{
				uuid: expect.any(String),
				markdown: "\n\n\n\n",
				correct: false,
			},
			{
				uuid: expect.any(String),
				markdown: "Juvenile pomacentrids were repeatedly surveyed across six weeks.",
				correct: false,
			},
			{
				uuid: expect.any(String),
				markdown:
					" This family was chosen because they are non-cryptic, highly abundant, and individuals can be visually surveyed accurately with minimal disturbance to the developing fish community.",
				correct: false,
			},
			{
				uuid: expect.any(String),
				markdown:
					" Compared to both no-loudspeaker reefs and dummy-loudspeaker reefs, acoustically enriched reefs attracted pomacentrids at a faster rate in the early stages of the experiment and maintained higher abundance throughout the forty days.",
				correct: false,
			},
			{
				uuid: expect.any(String),
				markdown:
					" After forty days, there were twice as many juvenile pomacentrids on acoustically enriched reefs than both categories of acoustically unmanipulated reefs.",
				correct: false,
			},
		]);
	});
});

describe("sortOrShuffleMultipleChoices", () => {
	it("works", () => {
		const { choices } = bundleMultipleChoice;
		const shuffledChoices = _.shuffle([...choices]);
		const sorted = sortOrShuffleMultipleChoices({ choices: shuffledChoices }); //?

		expect(sorted).toEqual([
			{ markdown: "$\\$-1,000$" },
			{ markdown: "$-100\\%$" },
			{ markdown: 1 },
			{ markdown: "$100\\%$" },
			{ markdown: "$1,000$" },
			{ markdown: "$\\$1,000.25$" },
		]);
	});

	it("shuffles and does not sort text", () => {
		const textValues = [
			{ markdown: "dirge" },
			{ markdown: "affectation" },
			{ markdown: "gauche" },
			{ markdown: "decorum" },
			{ markdown: "avarice" },
			{ markdown: "dilettante" },
		];
		const sortedTextValues = [
			{ markdown: "affectation" },
			{ markdown: "avarice" },
			{ markdown: "decorum" },
			{ markdown: "dilettante" },
			{ markdown: "dirge" },
			{ markdown: "gauche" },
		];

		const result = sortOrShuffleMultipleChoices({ choices: textValues }); //?

		expect(result).not.toEqual(sortedTextValues);
		expect(result).not.toEqual(textValues);
		expect(result).toEqual(expect.arrayContaining(textValues));
	});
});

describe("sortMultipleChoices", () => {
	it("works", () => {
		const { choices } = bundleMultipleChoice;
		const shuffledChoices = _.shuffle([...choices, { markdown: "C" }, { markdown: "A" }, { markdown: "B" }]);
		const sorted = sortMultipleChoices({ choices: shuffledChoices }); //?

		expect(sorted).toEqual([
			{ markdown: "$\\$-1,000$" },
			{ markdown: "$-100\\%$" },
			{ markdown: 1 },
			{ markdown: "$100\\%$" },
			{ markdown: "$1,000$" },
			{ markdown: "$\\$1,000.25$" },
			{ markdown: "A" },
			{ markdown: "B" },
			{ markdown: "C" },
		]);
	});

	it("sorts just text", () => {
		const textValues = [
			{ markdown: "dirge" },
			{ markdown: "affectation" },
			{ markdown: "gauche" },
			{ markdown: "decorum" },
			{ markdown: "avarice" },
			{ markdown: "dilettante" },
		];
		const sortedTextValues = [
			{ markdown: "affectation" },
			{ markdown: "avarice" },
			{ markdown: "decorum" },
			{ markdown: "dilettante" },
			{ markdown: "dirge" },
			{ markdown: "gauche" },
		];

		const result = sortMultipleChoices({ choices: textValues }); //?

		expect(result).toEqual(sortedTextValues);
		expect(result).not.toEqual(textValues);
	});

	it("sorts just numbers", () => {
		const { choices } = bundleMultipleChoice;
		const shuffledChoices = _.shuffle([...choices]);
		const sorted = sortMultipleChoices({ choices: shuffledChoices }); //?

		expect(sorted).toEqual([
			{ markdown: "$\\$-1,000$" },
			{ markdown: "$-100\\%$" },
			{ markdown: 1 },
			{ markdown: "$100\\%$" },
			{ markdown: "$1,000$" },
			{ markdown: "$\\$1,000.25$" },
		]);
	});
});

describe("sortChoices", () => {
	it("works", () => {
		const { choices } = bundleMultipleChoice;
		const shuffledChoices = _.shuffle([...choices, { markdown: "C" }, { markdown: "A" }, { markdown: "B" }]);

		const sortedResult = [
			{ markdown: "$\\$-1,000$" },
			{ markdown: "$-100\\%$" },
			{ markdown: 1 },
			{ markdown: "$100\\%$" },
			{ markdown: "$1,000$" },
			{ markdown: "$\\$1,000.25$" },
			{ markdown: "A" },
			{ markdown: "B" },
			{ markdown: "C" },
		];

		const sorted = sortChoices({
			choices: shuffledChoices,
			options: { sortOption: "mixedAsc" },
		}); //?
		expect(sorted).toEqual(sortedResult);

		const shuffled = sortChoices({
			choices: shuffledChoices,
			options: { sortOption: "shuffle" },
		}); //?
		expect(shuffled).not.toEqual(sortedResult);
		expect(shuffled).toEqual(expect.arrayContaining(sortedResult));

		const sortedOrShuffled = sortChoices({
			choices: shuffledChoices,
			options: { sortOption: "sortOrShuffle" },
		}); //?
		expect(sortedOrShuffled.slice(0, 6)).toEqual(sortedResult.slice(0, 6));
		expect(sortedOrShuffled).toEqual(expect.arrayContaining(sortedResult));
	});

	it("sorts just text", () => {
		const choices = _.shuffle([
			{ markdown: "dirge" },
			{ markdown: "affectation" },
			{ markdown: "gauche" },
			{ markdown: "decorum" },
			{ markdown: "avarice" },
			{ markdown: "dilettante" },
		]);
		const sortedResult = [
			{ markdown: "affectation" },
			{ markdown: "avarice" },
			{ markdown: "decorum" },
			{ markdown: "dilettante" },
			{ markdown: "dirge" },
			{ markdown: "gauche" },
		];

		const sorted = sortChoices({
			choices,
			options: { sortOption: "mixedAsc" },
		}); //?
		expect(sorted).toEqual(sortedResult);

		const shuffled = sortChoices({
			choices,
			options: { sortOption: "shuffle" },
		}); //?
		expect(shuffled).not.toEqual(sortedResult);
		expect(shuffled).toEqual(expect.arrayContaining(sortedResult));

		const sortedOrShuffled = sortChoices({
			choices,
			options: { sortOption: "sortOrShuffle" },
		}); //?
		expect(sortedOrShuffled).not.toEqual(sortedResult);
		expect(sortedOrShuffled).toEqual(expect.arrayContaining(sortedResult));
	});

	it("sorts just numbers", () => {
		const choices = _.shuffle(bundleMultipleChoice.choices);
		const sortedResult = [
			{ markdown: "$\\$-1,000$" },
			{ markdown: "$-100\\%$" },
			{ markdown: 1 },
			{ markdown: "$100\\%$" },
			{ markdown: "$1,000$" },
			{ markdown: "$\\$1,000.25$" },
		];

		const sorted = sortChoices({
			choices,
			options: { sortOption: "mixedAsc" },
		}); //?
		expect(sorted).toEqual(sortedResult);

		const shuffled = sortChoices({
			choices,
			options: { sortOption: "shuffle" },
		}); //?
		expect(shuffled).not.toEqual(sortedResult);
		expect(shuffled).toEqual(expect.arrayContaining(sortedResult));

		const sortedOrShuffled = sortChoices({
			choices,
			options: { sortOption: "sortOrShuffle" },
		}); //?
		expect(sortedOrShuffled).toEqual(sortedResult);
		expect(sortedOrShuffled).toEqual(expect.arrayContaining(sortedResult));
	});
});
