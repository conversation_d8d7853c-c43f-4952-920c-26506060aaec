const prompt = require("../prompt");
const bundleActEnglishMultipleChoice = require("../__mocks__/bundleActEnglishMultipleChoice");
const bundleClozeDeletionSingle = require("../__mocks__/bundleClozeDeletionSingle");
const bundleClozeDeletionTriple = require("../__mocks__/bundleClozeDeletionTriple");

describe("generateActEnglishMultipleChoicePrompt", () => {
	test("works", async () => {
		const result = prompt.generateActEnglishMultipleChoicePrompt({
			bundle: bundleActEnglishMultipleChoice,
			filledChoice: "is",
		});
		expect(result).toEqual(
			"Taking regular breaks <u>**is**</u> a good way to stay energized and focused while you work."
		);
	});
});

describe("generateClozeDeletionPrompt", () => {
	test("single", async () => {
		const result = prompt.generateClozeDeletionPrompt({
			bundle: bundleClozeDeletionSingle,
		});
		expect(result).toEqual(
			"To \\_\\_\\_**(1)**\\_\\_\\_ his colleagues, the minister of commerce reassured the private sector that the corruption investigation wouldn't interfere with normal business operations."
		);
	});

	test("triple", async () => {
		const result = prompt.generateClozeDeletionPrompt({
			bundle: bundleClozeDeletionTriple,
		});
		expect(result).toEqual(
			"The employee shrugged his shoulders in a way that managed to toe the \\_\\_\\_**(1)**\\_\\_\\_ line between indifference and disrespect. The supervisor had half a mind to chastise him for the \\_\\_\\_**(2)**\\_\\_\\_, but instead chose to interpret the gesture as \\_\\_\\_**(3)**\\_\\_\\_."
		);
	});
});
