const _ = require("lodash");
const keymirror = require("keymirror");
const { v4: uuidV4 } = require("uuid");

const generateChoicesPassageChooseSentence = ({ bundle }) => {
	const { passage } = bundle;

	const splits = passage.trim().split("|");

	const choices = splits.reduce((acc, piece) => {
		if (piece.length === 0) {
			return acc;
		}

		const firstChar = piece[0];
		const lastChar = piece[piece.length - 1];

		const { correct, markdown } = (() => {
			if (firstChar !== "=" || lastChar !== "=") {
				return {
					correct: false,
					markdown: piece,
				};
			}

			return {
				correct: true,
				markdown: piece.slice(1, -1),
			};
		})();

		const choice = {
			uuid: uuidV4(),
			markdown,
			correct,
		};

		acc.push(choice);
		return acc;
	}, []);

	return choices;
};

const getNumberFromChoice = (choice) => {
	if (typeof choice === "number") return choice;

	const regex = new RegExp("[^-0-9.]", "g");
	const number = choice.replace(regex, "");
	const result = parseFloat(number);

	if (Number.isNaN(result)) return null;

	return result;
};

const getAnnotatedChoices = ({ choices }) => {
	const annotatedChoices = _.map(choices, (choice) => {
		const { markdown } = choice;
		const number = getNumberFromChoice(markdown);
		return {
			choice,
			markdown,
			number,
		};
	});

	const [annotatedTextChoices, annotatedNumberChoices] = _.partition(
		annotatedChoices,
		(annotatedChoice) => annotatedChoice.number === null
	);

	return { annotatedChoices, annotatedTextChoices, annotatedNumberChoices };
};

const sortAnnotatedNumberChoices = (annotatedNumberChoices) =>
	annotatedNumberChoices.sort((a, b) => a.number - b.number);

const sortAnnotatedTextChoices = (annotatedTextChoices) =>
	annotatedTextChoices.sort((a, b) => (a.markdown > b.markdown ? 1 : -1));

const sortMultipleChoices = ({ choices }) => {
	const { annotatedTextChoices, annotatedNumberChoices } = getAnnotatedChoices({
		choices,
	});

	const sortedNumbers = sortAnnotatedNumberChoices(annotatedNumberChoices);
	const sortedText = sortAnnotatedTextChoices(annotatedTextChoices);
	const combined = [...sortedNumbers, ...sortedText];

	const result = _.map(combined, (annotatedChoice) => annotatedChoice.choice);
	return result;
};

const sortOrShuffleMultipleChoices = ({ choices: choicesRaw }) => {
	const [[noChangeChoice], choices] = _.partition(choicesRaw, { markdown: "NO CHANGE" });

	const { annotatedTextChoices, annotatedNumberChoices } = getAnnotatedChoices({
		choices,
	});
	const sortedNumbers = sortAnnotatedNumberChoices(annotatedNumberChoices);
	const shuffledText = _.shuffle(annotatedTextChoices);
	const combined = [...sortedNumbers, ...shuffledText];
	const result = _.map(combined, (annotatedChoice) => annotatedChoice.choice);

	if (noChangeChoice) result.unshift(noChangeChoice);

	return result;
};

const SORT_OPTIONS = keymirror({
	mixedAsc: null, // sorts numbers ascending, text alphabetically
	shuffle: null, // shuffles all choices
	sortOrShuffle: null, // sorts numbers, shuffles text
});

const sortChoicesByOption = ({ choices, sortOption }) => {
	switch (sortOption) {
		case SORT_OPTIONS.mixedAsc: {
			return sortMultipleChoices({ choices });
		}
		case SORT_OPTIONS.shuffle: {
			return _.shuffle(choices);
		}
		case SORT_OPTIONS.sortOrShuffle: {
			return sortOrShuffleMultipleChoices({ choices });
		}
		default: {
			return choices;
		}
	}
};

const sortChoices = ({ choices, options }) => {
	const { noShuffle, sortOnly, sortOption } = options;

	if (sortOption) {
		return sortChoicesByOption({ choices, sortOption });
	}

	if (sortOnly) {
		return sortMultipleChoices({ choices });
	} else if (!noShuffle) {
		return sortOrShuffleMultipleChoices({ choices });
	}

	return choices;
};

module.exports = {
	generateChoicesPassageChooseSentence,
	sortChoices,
	sortOrShuffleMultipleChoices,
	sortMultipleChoices,
};
