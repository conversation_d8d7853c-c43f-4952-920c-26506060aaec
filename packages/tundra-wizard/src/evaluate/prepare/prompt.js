const generateActEnglishMultipleChoicePrompt = ({ bundle, filledChoice }) => {
	const { prompt: promptRaw } = bundle;

	const prompt = promptRaw.replace(/___+/g, (_match) => {
		const blank = `<u>**${filledChoice}**</u>`;
		return blank;
	});

	return prompt;
};

const generateClozeDeletionPrompt = ({ bundle }) => {
	const { prompt: promptRaw } = bundle;

	let deletionCounter = 0;

	const prompt = promptRaw.replace(/___+/g, (_match) => {
		deletionCounter += 1;
		const blank = `\\_\\_\\_**(${deletionCounter})**\\_\\_\\_`;
		return blank;
	});

	return prompt;
};

module.exports = {
	generateActEnglishMultipleChoicePrompt,
	generateClozeDeletionPrompt,
};
