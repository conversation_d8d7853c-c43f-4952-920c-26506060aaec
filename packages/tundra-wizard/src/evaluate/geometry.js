const _ = require("lodash");
const random = require("./random");

const init = ({ x }) => {
	const r = random({ x });

	const generateTriangleFromPerimeter = (perimeter) => {
		let remaining = perimeter;

		const minSide = perimeter % 2 === 0 ? 2 : 1; //?
		const halfFloor = Math.floor((perimeter - minSide) / 2); //?
		const a = r.ii(minSide, halfFloor); //?

		remaining = perimeter - a; //?
		const b = r.ii(remaining - halfFloor, halfFloor); //?
		const c = remaining - b; //?

		return [a, b, c];
	};

	return {
		generateTriangleFromPerimeter,
	};
};

module.exports = init;
