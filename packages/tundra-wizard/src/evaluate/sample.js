const lodash = require("lodash");
const seedrandom = require("seedrandom");

const init = ({ x }) => {
	const { seed } = x;
	seedrandom(seed, { global: true });
	const _ = lodash.runInContext();

	const CITIES = [
		"New York City",
		"Los Angeles",
		"Chicago",
		"Houston",
		"Phoenix",
		"Philadelphia",
		"San Antonio",
		"San Diego",
		"Dallas",
		"San Jose",
	];

	const MONTHS = [
		"January",
		"February",
		"March",
		"April",
		"May",
		"June",
		"July",
		"August",
		"September",
		"October",
		"November",
		"December",
	];

	const SHORT_MONTHS = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

	const NAMES = [
		"<PERSON><PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON><PERSON>",
		"<PERSON><PERSON>",
		"<PERSON><PERSON>",
		"<PERSON>",
		"Arm<PERSON>",
		"Arrow",
		"<PERSON><PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON><PERSON>",
		"<PERSON><PERSON><PERSON>",
		"<PERSON>",
		"<PERSON><PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON>",
		"<PERSON>",
		"<PERSON><PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON><PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON><PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON><PERSON>",
		"<PERSON>",
		"<PERSON><PERSON>",
		"<PERSON>",
		"<PERSON><PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON><PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON><PERSON>",
		"Jazz",
		"Jordan",
		"Jules",
		"Justice",
		"Kamryn",
		"Karter",
		"Kendall",
		"Kingsley",
		"Kirby",
		"Kyrie",
		"Lake",
		"Landry",
		"Laramie",
		"Lennox",
		"Linden",
		"London",
		"Lyric",
		"Marley",
		"Marlo",
		"Memphis",
		"Mercury",
		"Merit",
		"Milan",
		"Miller",
		"Monroe",
		"Morgan",
		"Murphy",
		"Navy",
		"Nicky",
		"Oakley",
		"Ocean",
		"Oswin",
		"Parker",
		"Payton",
		"Perry",
		"Peyton",
		"Phoenix",
		"Poet",
		"Quinn",
		"Raleigh",
		"Ramsey",
		"Rebel",
		"Reese",
		"Reilly",
		"Remington",
		"Remy",
		"Revel",
		"Riley",
		"Rio",
		"River",
		"Robin",
		"Rory",
		"Rowan",
		"Royal",
		"Rumi",
		"Rylan",
		"Sage",
		"Sailor",
		"Sawyer",
		"Scout",
		"Seneca",
		"Shay",
		"Shiloh",
		"Sidney",
		"Skyler",
		"Spencer",
		"Storm",
		"Sutton",
		"Tatum",
		"Taylor",
		"Tennyson",
		"Texas",
		"Timber",
		"Tobin",
		"Tory",
		"Valentine",
		"Wilder",
		"Wynn",
		"Zephyr",
	];

	const FOODS_LIST = [
		"spaghetti",
		"pizza",
		"mac and cheese",
		"apple sauce",
		"ice cream",
		"cupcake",
		"burger",
		"french fry",
		"salad",
		"bean",
		"rice",
		"taco",
		"vegetable",
		"strawberry",
		"banana",
		"apple",
		"mango",
		"cranberry",
		"peach",
		"lychee",
		"grape",
		"papaya",
		"watermelon",
	];

	const city = () => {
		return _.sample(CITIES);
	};

	const givenName = (num = 1) => {
		return _.sampleSize(NAMES, num);
	};

	const month = () => {
		return _.sample(MONTHS);
	};

	const shortMonth = () => {
		return _.sample(SHORT_MONTHS);
	};

	const food = () => {
		return _.sample(FOODS_LIST);
	};

	return {
		city,
		gn: givenName,
		food,
		month,
		shortMonth,
	};
};

module.exports = init;
