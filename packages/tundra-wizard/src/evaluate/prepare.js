const _ = require("lodash");
const { v4: uuidV4 } = require("uuid");
const { generateChoicesPassageChooseSentence, sortChoices } = require("./prepare/choices");
const { generateActEnglishMultipleChoicePrompt, generateClozeDeletionPrompt } = require("./prepare/prompt");

const removeInvalidChoices = (choices) => {
	const validChoices = choices.filter((choice) => {
		const markdown = _.get(choice, "markdown");
		const isInvalid = markdown === undefined || markdown === null || markdown === NaN || /^\s*$/.test(markdown);
		return !isInvalid;
	});
	return validChoices;
};

const prepareActEnglishMultipleChoice = (bundle) => {
	const prepared = _.cloneDeep(bundle);
	const { answer, distractors } = prepared;

	let choices = [];
	choices.push({
		uuid: uuidV4(),
		markdown: answer,
		correct: true,
	});
	const distractorChoices = distractors.map((distractor) => ({
		uuid: uuidV4(),
		markdown: distractor,
	}));
	choices.push(...distractorChoices);
	choices = _.shuffle(choices);

	const filledChoice = choices[0].markdown;
	choices[0].markdown = "NO CHANGE";

	prepared.prompt = generateActEnglishMultipleChoicePrompt({
		bundle: prepared,
		filledChoice,
	});
	prepared.choices = choices;
	return prepared;
};

const prepareMultipleChoice = (bundle) => {
	const prepared = _.cloneDeep(bundle);
	const options = prepared.options || {};

	if (prepared.choices) {
		prepared.choices = _.uniqBy(prepared.choices, (choice) => choice.markdown);
		prepared.choices = prepared.choices.map((choice) => ({
			...choice,
			markdown: choice.markdown.toString(),
			uuid: uuidV4(),
		}));
	} else {
		prepared.distractors = _.uniq(prepared.distractors);
		prepared.distractors = _.without(prepared.distractors, prepared.answer);

		prepared.distractors = prepared.distractors.map((distractor) => ({
			uuid: uuidV4(),
			markdown: distractor.toString(),
		}));

		prepared.answer = {
			uuid: uuidV4(),
			markdown: prepared.answer.toString(),
			correct: true,
		};

		prepared.choices = [prepared.answer, ...prepared.distractors];
	}

	prepared.choices = removeInvalidChoices(prepared.choices);
	prepared.choices = sortChoices({ choices: prepared.choices, options });

	return prepared;
};

const prepareSelectMultiple = (bundle) => {
	const prepared = _.cloneDeep(bundle);
	const options = prepared.options || {};

	prepared.choices = _.uniqBy(prepared.choices, (choice) => choice.markdown);
	prepared.choices = prepared.choices.map((choice) => ({
		...choice,
		markdown: choice.markdown.toString(),
		uuid: uuidV4(),
	}));

	prepared.choices = removeInvalidChoices(prepared.choices);
	prepared.choices = sortChoices({ choices: prepared.choices, options });

	return prepared;
};

const preparePassageChooseSentence = (bundle) => {
	const prepared = _.cloneDeep(bundle);
	prepared.choices = generateChoicesPassageChooseSentence({ bundle: prepared });
	prepared.choices = removeInvalidChoices(prepared.choices);
	return prepared;
};

const prepareClozeDeletion = (bundle) => {
	const prepared = _.cloneDeep(bundle);
	prepared.choices = prepared.choices.map((choiceSet) =>
		_.shuffle(choiceSet).map((choice) => ({
			...choice,
			markdown: choice.markdown.toString(),
			uuid: uuidV4(),
		}))
	);
	prepared.prompt = generateClozeDeletionPrompt({ bundle: prepared });
	return prepared;
};

const prepareQuantityComparison = (bundle) => {
	const prepared = _.cloneDeep(bundle);

	const choices = {
		a: {
			uuid: uuidV4(),
			markdown: "Quantity A is greater",
		},
		b: {
			uuid: uuidV4(),
			markdown: "Quantity B is greater",
		},
		c: {
			uuid: uuidV4(),
			markdown: "The two quantities are equal",
		},
		d: {
			uuid: uuidV4(),
			markdown: "Relationship cannot be determined",
		},
	};

	choices[prepared.answer].correct = true;
	prepared.choices = [choices.a, choices.b, choices.c, choices.d];

	return prepared;
};

const prepareNumeric = (bundle) => {
	const prepared = _.cloneDeep(bundle);
	return prepared;
};

const ITEM_TYPE_MAP = {
	actEnglishMultipleChoice: prepareActEnglishMultipleChoice,
	actPassageChooseOne: prepareMultipleChoice,
	clozeDeletion: prepareClozeDeletion,
	multipleChoice: prepareMultipleChoice,
	numeric: prepareNumeric,
	passageChooseMany: prepareSelectMultiple,
	passageChooseOne: prepareMultipleChoice,
	passageChooseSentence: preparePassageChooseSentence,
	passageInsertAt: prepareMultipleChoice,
	quantityComparison: prepareQuantityComparison,
	selectMultiple: prepareSelectMultiple,
	sentenceEquivalence: prepareSelectMultiple,
};

const prepare = (bundle) => {
	const { itemType: rawItemType } = bundle;
	const itemType = rawItemType || "multipleChoice";

	const prepareBundle = {
		...bundle,
		itemType,
	};

	const prepareFn = ITEM_TYPE_MAP[itemType];
	const prepared = prepareFn(prepareBundle);

	return prepared;
};

module.exports = {
	prepare,
};
