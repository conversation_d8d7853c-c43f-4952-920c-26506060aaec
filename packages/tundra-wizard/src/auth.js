const _find = require("lodash/find");
const _some = require("lodash/some");
const SUBSCRIPTION_STATUS = require("./subscriptionStatus");
const PASSWORD_POLICY = require("./passwordPolicy");
const SCOPES = require("./scopes");

const isAdmin = ({ user }) => user && _some(user.grants, (grant) => grant.scope === SCOPES.admin);
const isSysAdmin = ({ user }) => user && _some(user.grants, (grant) => grant.scope === SCOPES.sysadmin);
const isAuthor = ({ user }) => user && _some(user.grants, (grant) => grant.scope === SCOPES.author);
const isEditor = ({ user }) => user && _some(user.grants, (grant) => grant.scope === SCOPES.editor);
const isManager = ({ user }) => user && _some(user.grants, (grant) => grant.scope === SCOPES.manage);
const isMember = ({ user }) => user && _some(user.grants, (grant) => grant.scope === SCOPES.member);
const isSales = ({ user }) => user && _some(user.grants, (grant) => grant.scope === SCOPES.sales);
const isContent = ({ user }) => user && _some(user.grants, (grant) => grant.scope === SCOPES.content);
const isSystem = ({ user }) => user && user.email === "<EMAIL>";
const isStaff = ({ user }) =>
	user &&
	(isAdmin({ user }) ||
		isSysAdmin({ user }) ||
		isAuthor({ user }) ||
		isContent({ user }) ||
		isEditor({ user }) ||
		isManager({ user }) ||
		isSales({ user }) ||
		isMember({ user }));

const isAchievable = ({ user }) => isSystem({ user }) || isAdmin({ user }) || isMember({ user });

const canAuthor = ({ user, product, content }) => {
	if (!user) {
		return false;
	}

	if (isAchievable({ user })) {
		return true;
	}

	return _some(user.grants, (grant) => {
		if (grant.scope !== SCOPES.author) {
			return false;
		}

		if (grant.targetType !== "product") {
			return false;
		}

		if (product) {
			const canAuthorProduct = grant.targetUuid === product.uuid;
			const canAuthorParentProduct = grant.targetUuid === product.parentProductUuid;
			return canAuthorProduct || canAuthorParentProduct;
		}

		if (content) {
			return grant.targetUuid === content.productUuid;
		}

		return false;
	});
};

const canEdit = ({ user, product, content }) => {
	if (!user) {
		return false;
	}

	if (isSystem({ user }) || isAdmin({ user })) {
		return true;
	}

	if (canAuthor({ user, product, content })) {
		return true;
	}

	return _some(user.grants, (grant) => {
		if (grant.scope !== SCOPES.editor) {
			return false;
		}

		if (grant.targetType !== "product") {
			return false;
		}

		if (product) {
			return grant.targetUuid === product.uuid;
		}

		if (content) {
			return grant.targetUuid === content.productUuid;
		}

		return false;
	});
};

const canManage = ({ user, organization }) => {
	if (!user || !organization) {
		return false;
	}

	if (isAchievable({ user })) {
		return true;
	}

	return _some(user.grants, (grant) => {
		if (grant.scope !== SCOPES.manage) {
			return false;
		}

		if (grant.targetType !== "organization") {
			return false;
		}

		return grant.targetUuid === organization.uuid;
	});
};

const findActiveSubscription = ({ user, product }) => {
	if (!user) {
		return null;
	}

	const { subscriptions } = user;

	const activeSubscription = _find(
		subscriptions,
		(subscription) =>
			subscription.productUuid === product.uuid &&
			subscription.status === SUBSCRIPTION_STATUS.full &&
			new Date(subscription.expiresAt).getTime() + 86400000 > Date.now()
	);

	return activeSubscription;
};

const hasFullAccess = ({ user, product }) => {
	return findActiveSubscription({ user, product }) || isManager({ user, product }) || canAuthor({ user, product });
};

const validatePassword = ({ password }) => {
	const errors = [];
	for (const { regex, message } of PASSWORD_POLICY) {
		const isValid = regex.test(password);
		if (!isValid) errors.push({ detail: message, pointer: "#/password" });
	}

	return { errors };
};

module.exports = {
	canAuthor,
	canEdit,
	canManage,
	findActiveSubscription,
	hasFullAccess,
	isAchievable,
	isAdmin,
	isSysAdmin,
	isAuthor,
	isEditor,
	isSales,
	isStaff,
	isContent,
	isSystem,
	isManager,
	isMember,
	validatePassword,
};
