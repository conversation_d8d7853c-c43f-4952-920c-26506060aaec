const _ = require("lodash");
const { PRODUCT_SLUG_MIN_SCORES } = require("../constants");

const SCORE_BUFFER = 0.05;

const getScoreSummary = ({ productSlug, score, variant }) => {
	const productMinScores = PRODUCT_SLUG_MIN_SCORES[productSlug]; //?
	const defaultMinScore = productMinScores?.default || PRODUCT_SLUG_MIN_SCORES.default;
	const minScore = _.get(productMinScores, variant, defaultMinScore); //?

	if (score == null) {
		return { summary: "(none)", theme: "default" };
	}

	if (score > minScore + SCORE_BUFFER) {
		return { summary: "pass", theme: "success" };
	}

	if (score >= minScore) {
		return { summary: "warn", theme: "warn" };
	}

	return { summary: "fail", theme: "error" };
};

module.exports = {
	getScoreSummary,
};
