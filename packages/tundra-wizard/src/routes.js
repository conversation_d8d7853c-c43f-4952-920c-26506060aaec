const { include } = require("named-urls");

module.exports = {
	auth: include("/auth", {
		forgotPassword: "forgot-password",
		resetPassword: "reset-password/:token",
		reset: "reset",
		signIn: "sign-in",
		signOut: "sign-out",
		signUp: "sign-up",
		talkSso: "talk-sso",
		welcome: "welcome/:userUuid",
	}),
	enroll: include("/enroll/:code", {
		prefix: "",
		overview: "overview",
		purchase: "purchase",
	}),
	magic: "/magic/:data",
	redeem: "/redeem",
	study: include("/study/:productSlug", {
		account: "account",
		catalog: "catalog",
		content: "content/:contentUuid",
		dashboard: "dashboard",
		essay: "essay/:essayResponseUuid",
		exam: "exams/:userExamUuid",
		examFeedback: "exam-feedback/:studyPlanUuid",
		exams: "exams",
		tipsForSuccess: "exams/tips-for-success",
		externalExam: "external-exam/:userExternalExamUuid",
		feedback: "feedback",
		learn: include("learn/", {
			prefix: "",
			content: ":contentUuid",
		}),
		onboarding: "onboarding",
		overview: "overview",
		partnerCourses: "partner-courses/:slug",
		purchase: "purchase",
		quiz: "quiz/:quizCode",
		resources: "resources",
		review: "review",
		search: "search",
		subscription: "subscription",
		tutoring: "tutoring",
		tutorsVideosResources: "tutors-videos-resources",
	}),

	staff: include("/staff", {
		prefix: "",
		awardNominations: "award-nominations",
		charges: ":partnerSlug/charges",
		dashboard: "dashboard",
		organizations: include("organizations/", {
			prefix: "",
			new: "new",
		}),
		organization: include("organizations/:organizationSlug", {
			prefix: "",
			overview: "overview",
			groups: include("groups", {
				overview: "",
				detail: ":groupSlug",
			}),
			members: "members",
			realtime: "realtime",
			staff: "staff",
			invoice: "invoice",
			support: "support",
			enrollmentConfig: "enrollment-config",
			pricingConfig: "pricing-config",
		}),
		products: include("products/", {
			prefix: "",
			new: "new",
		}),
		product: include("products/:productSlug", {
			content: include("content", {
				overview: "",
				edit: ":contentUuid",
			}),
			edit: "edit",
			files: "files",
			flashcards: "flashcards",
			grants: "grants",
			overview: "overview",
			programPlan: "program-plan",
			studyPlanFeedback: "study-plan-feedback",
		}),
		grants: "grants",
		promoCodes: include("promo-codes", {
			new: "new",
			overview: "",
		}),
		quizEditor: include("quiz-editor/", {
			prefix: "",
			new: "new",
		}),
		search: "search",
		signature: "signature",
		studyPlanFeedback: "study-plan-feedback",
		system: "system",
		tutor: "tutor",
		users: include("users", {
			overview: "",
			detail: ":userUuid",
		}),
	}),

	studyReport: include("/study-report/:userUuid/:productSlug", {
		activity: "activity",
		fullReport: "full-report",
		overview: "",
	}),
};
