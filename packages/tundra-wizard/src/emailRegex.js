const _ = require("lodash");
const { PERSONAL_DOMAINS } = require("./emailDomains");

const getIsPersonalEmail = ({ email }) => {
	const lowerCaseEmail = email.toLowerCase();
	const isPersonalEmail = _.some(PERSONAL_DOMAINS, (domain) => lowerCaseEmail.endsWith(domain));
	return isPersonalEmail;
};

const getIsBusinessEmail = ({ email }) => {
	const isPersonalEmail = getIsPersonalEmail({ email });
	if (isPersonalEmail) {
		return false;
	}

	const isEduEmail = getIsEduEmail({ email });
	if (isEduEmail) {
		return false;
	}

	return true;
};

const getIsEduEmail = ({ email }) => {
	return /\.edu$/i.test(email);
};

const getIsMatchingEmail = ({ email, regex: regexRaw }) => {
	const regex = new RegExp(regexRaw, "i");
	return regex.test(email);
};

const getIsMilEmail = ({ email }) => {
	return /\.mil/i.test(email);
};

module.exports = {
	getIsBusinessEmail,
	getIsEduEmail,
	getIsMatchingEmail,
	getIsMilEmail,
};
