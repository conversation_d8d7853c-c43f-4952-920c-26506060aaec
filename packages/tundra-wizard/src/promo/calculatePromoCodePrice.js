const PromoCodeType = require("./promoCodeType");
const roundToCurrency = require("./roundToCurrency");

const calculatePromoCodePrice = ({ price, promoCode }) => {
	if (!promoCode) {
		return roundToCurrency(price);
	}

	const { type, amount } = promoCode;

	let calculatedPrice;
	switch (type) {
		case PromoCodeType.discountPercent: {
			calculatedPrice = price * (1 - amount);
			break;
		}
		case PromoCodeType.discountPrice: {
			calculatedPrice = price - amount;
			break;
		}
		case PromoCodeType.price: {
			calculatedPrice = amount;
			break;
		}
		default: {
			calculatedPrice = price;
		}
	}

	const finalPrice = roundToCurrency(calculatedPrice);
	return finalPrice;
};

module.exports = calculatePromoCodePrice;
