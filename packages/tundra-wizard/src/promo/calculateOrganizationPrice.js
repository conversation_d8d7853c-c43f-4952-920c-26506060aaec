const _ = require("lodash");
const calculatePromoCodePrice = require("./calculatePromoCodePrice");

const calculateOrganizationPrice = ({ product, organizationPricings }) => {
	// 20250527CL: Product will not be defined for global discounts.
	if (!product) return "-";

	const { price, uuid: productUuid } = product;
	const priceToUse = price ?? 100;
	const globalConfig = _.find(organizationPricings, { productUuid: null });
	const productConfig = _.find(organizationPricings, { productUuid });
	const configToUse = productConfig ?? globalConfig;

	// 20250527JP: This should never happen because we're iterating over configs, but just in case.
	if (!configToUse) return priceToUse;

	const { type, amount } = configToUse;
	// 20250527JP: Promo code type and amount are the same as the organizationPricing config type and amount.
	const simulatedPromoCode = { type, amount };
	const discountedPrice = calculatePromoCodePrice({
		price: priceToUse,
		promoCode: simulatedPromoCode,
	});

	return discountedPrice;
};

module.exports = calculateOrganizationPrice;
