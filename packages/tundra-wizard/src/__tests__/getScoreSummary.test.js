const { getScoreSummary } = require("../examHelpers");

describe("getScoreSummary", () => {
	it("works for finra-sie", () => {
		const result1 = getScoreSummary({
			productSlug: "finra-sie",
			score: 0.69,
			variant: null,
		}); //?
		expect(result1.theme).toBe("error");
		expect(result1.summary).toBe("fail");

		const result2 = getScoreSummary({
			productSlug: "finra-sie",
			score: 0.7,
			variant: "final-1",
		}); //?
		expect(result2.theme).toBe("warn");
		expect(result2.summary).toBe("warn");

		const result3 = getScoreSummary({
			productSlug: "finra-sie",
			score: 0.76,
			variant: "final-2",
		}); //?
		expect(result3.theme).toBe("success");
		expect(result3.summary).toBe("pass");

		const result4 = getScoreSummary({
			productSlug: "finra-sie",
			score: 0.7,
			variant: "common-stock",
		}); //?
		expect(result4.theme).toBe("warn");
		expect(result4.summary).toBe("warn");
	});
	it("works for finra-series-7", () => {
		const result1 = getScoreSummary({
			productSlug: "finra-series-7",
			score: 0.7,
			variant: null,
		}); //?
		expect(result1.theme).toBe("error");
		expect(result1.summary).toBe("fail");

		const result2 = getScoreSummary({
			productSlug: "finra-series-7",
			score: 0.72,
			variant: "final-1",
		}); //?
		expect(result2.theme).toBe("warn");
		expect(result2.summary).toBe("warn");

		const result3 = getScoreSummary({
			productSlug: "finra-series-7",
			score: 0.8,
			variant: "final-2",
		}); //?
		expect(result3.theme).toBe("success");
		expect(result3.summary).toBe("pass");

		const result4 = getScoreSummary({
			productSlug: "finra-series-7",
			score: 0.75,
			variant: "common-stock",
		}); //?
		expect(result4.theme).toBe("warn");
		expect(result4.summary).toBe("warn");
	});

	it("works for gre-v2", () => {
		const result1 = getScoreSummary({
			productSlug: "gre-v2",
			score: 0.5,
			variant: "quant-long",
		}); //?
		expect(result1.theme).toBe("error");
		expect(result1.summary).toBe("fail");

		const result2 = getScoreSummary({
			productSlug: "gre-v2",
			score: 0.59,
			variant: "quant-long",
		}); //?
		expect(result2.theme).toBe("error");
		expect(result2.summary).toBe("fail");

		const result3 = getScoreSummary({
			productSlug: "gre-v2",
			score: 0.6,
			variant: "verbal-short",
		}); //?
		expect(result3.theme).toBe("warn");
		expect(result3.summary).toBe("warn");

		const result4 = getScoreSummary({
			productSlug: "gre-v2",
			score: 0.7,
			variant: "verbal-long",
		}); //?
		expect(result4.theme).toBe("success");
		expect(result4.summary).toBe("pass");
	});

	it("works for act", () => {
		const result1 = getScoreSummary({
			productSlug: "act",
			score: 0.5,
			variant: "english",
		}); //?
		expect(result1.theme).toBe("error");
		expect(result1.summary).toBe("fail");

		const result2 = getScoreSummary({
			productSlug: "act",
			score: 0.59,
			variant: "math",
		}); //?
		expect(result2.theme).toBe("error");
		expect(result2.summary).toBe("fail");

		const result3 = getScoreSummary({
			productSlug: "act",
			score: 0.6,
			variant: "reading",
		}); //?
		expect(result3.theme).toBe("warn");
		expect(result3.summary).toBe("warn");

		const result4 = getScoreSummary({
			productSlug: "act",
			score: 0.7,
			variant: "science",
		}); //?
		expect(result4.theme).toBe("success");
		expect(result4.summary).toBe("pass");
	});
});
