const { getIsEduEmail, getIsMatchingEmail, getIsBusinessEmail } = require("../emailRegex");

describe("getIsBusinessEmail", () => {
	test("works for gmail", async () => {
		const result1 = getIsBusinessEmail({ email: "<EMAIL>" });
		const result2 = getIsBusinessEmail({ email: "<EMAIL>" });
		const result3 = getIsBusinessEmail({ email: "<EMAIL>" });
		expect(result1).toEqual(false);
		expect(result2).toEqual(false);
		expect(result3).toEqual(false);
	});

	test("works for ymail", async () => {
		const result = getIsBusinessEmail({ email: "<EMAIL>" });
		expect(result).toEqual(false);
	});

	test("works for edu", async () => {
		const result = getIsBusinessEmail({ email: "<EMAIL>" });
		expect(result).toEqual(false);
	});

	test("works", async () => {
		const result1 = getIsBusinessEmail({ email: "<EMAIL>" });
		const result2 = getIsBusinessEmail({ email: "<EMAIL>" });
		const result3 = getIsBusinessEmail({ email: "<EMAIL>" });
		const result4 = getIsBusinessEmail({ email: "<EMAIL>" });
		expect(result1).toEqual(true);
		expect(result2).toEqual(true);
		expect(result3).toEqual(true);
		expect(result4).toEqual(true);
	});
});

describe("getIsEduEmail", () => {
	test("works for gmail", async () => {
		const result = getIsEduEmail({ email: "<EMAIL>" }); //?
		expect(result).toEqual(false);
	});

	test("works for organizations", async () => {
		const result = getIsEduEmail({ email: "<EMAIL>" }); //?
		expect(result).toEqual(false);
	});

	test("works for edu", async () => {
		const result = getIsEduEmail({ email: "<EMAIL>" }); //?
		expect(result).toEqual(true);
	});
});

describe("getIsMatchingEmail", () => {
	test("works for du", async () => {
		const email = "<EMAIL>";
		const regex = "@du\\.edu$";

		const result = getIsMatchingEmail({ email, regex }); //?
		expect(result).toEqual(true);

		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
	});
	test("works for pinnacle-llc", async () => {
		const email = "<EMAIL>";
		const regex = "@pinnacle-llc\\.com$";

		const result = getIsMatchingEmail({ email, regex }); //?
		expect(result).toEqual(true);

		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
	});
	test("works for nm", async () => {
		const email = "<EMAIL>";
		const regex = "@nm\\.com$";

		const result = getIsMatchingEmail({ email, regex }); //?
		expect(result).toEqual(true);

		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
	});
	test("works for edwardjones", async () => {
		const email = "<EMAIL>";
		const regex = "@edwardjones\\.com$";

		const result = getIsMatchingEmail({ email, regex }); //?
		expect(result).toEqual(true);

		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
	});
	test("works for cir2", async () => {
		const email = "<EMAIL>";
		const regex = "@cir2\\.com$";

		const result = getIsMatchingEmail({ email, regex }); //?
		expect(result).toEqual(true);

		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
	});
	test("works for ampf", async () => {
		const email = "<EMAIL>";
		const regex = "@ampf\\.com$";

		const result = getIsMatchingEmail({ email, regex }); //?
		expect(result).toEqual(true);

		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
	});
	test("works for americandiscoverycapital", async () => {
		const email = "<EMAIL>";
		const regex = "@americandiscoverycapital\\.com$";

		const result = getIsMatchingEmail({ email, regex }); //?
		expect(result).toEqual(true);

		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
	});
	test("works for prudential", async () => {
		const email = "<EMAIL>";
		const regex = "@prudential\\.com$";

		const result = getIsMatchingEmail({ email, regex }); //?
		expect(result).toEqual(true);

		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
	});
	test("works for allianceadvisorygroup", async () => {
		const email = "<EMAIL>";
		const regex = "@allianceadvisorygroup\\.com$";

		const result = getIsMatchingEmail({ email, regex }); //?
		expect(result).toEqual(true);

		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
	});
	test("works for planningalliance", async () => {
		const email = "<EMAIL>";
		const regex = "@planningalliance\\.com$";

		const result = getIsMatchingEmail({ email, regex }); //?
		expect(result).toEqual(true);

		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
	});
	test("works for barnumfg", async () => {
		const email = "<EMAIL>";
		const regex = "@barnumfg\\.com$";

		const result = getIsMatchingEmail({ email, regex }); //?
		expect(result).toEqual(true);

		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
		expect(getIsMatchingEmail({ email: "<EMAIL>", regex })).toEqual(false);
	});
});
