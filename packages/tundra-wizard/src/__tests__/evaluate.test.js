const { evaluate } = require("../evaluate");

describe("evaluate", () => {
	test("rerolls for RandomizeErrors", () => {
		let error;
		let result;

		try {
			for (let i = 0; i < 100; i += 1) {
				result = evaluate(`"use strict";

          var a = A.r.intInclusive(1, 3);
          
          if (a === 3) A.u.throwRandomizeError('This should reroll');
          var b = 1,
            c = 2,
            d = 3,
            e = 'This is a prompt',
            f = 'This is an explanation',
            g = [1, c, d];
          ({
            prompt: e,
            answer: a,
            distractors: g,
            explanation: f
          });`); //?
			}
		} catch (e) {
			error = e;
		}

		expect(result.rerollCount).toBeLessThan(10);
		expect(result.bundle).toBeTruthy();
		expect(error).toBe(undefined);
	});

	test("returns after N rerolls", () => {
		let error;
		let result;

		const errorMessage = "This should fail.";
		try {
			for (let i = 0; i < 100; i += 1) {
				result = evaluate(`"use strict";

          var a = A.r.intInclusive(1, 1);
          
          if (a === 1) A.u.throwRandomizeError('${errorMessage}');
          var b = 1,
            c = 2,
            d = 3,
            e = 'This is a prompt',
            f = 'This is an explanation',
            g = [1, c, d];
          ({
            prompt: e,
            answer: a,
            distractors: g,
            explanation: f
          });`); //?
			}
		} catch (e) {
			error = e;
		}

		expect(result.rerollCount).toBe(10);
		expect(result.bundle).toBe(undefined);
		expect(result.errors.length).toBe(1);
		expect(result.errors).toEqual([errorMessage]);
		expect(error).toBe(undefined);
	});

	test("works for throwing errors directly", () => {
		let error;
		let result;

		const errorMessage = "This should fail.";
		try {
			for (let i = 0; i < 100; i += 1) {
				result = evaluate(`"use strict";

          var a = A.r.intInclusive(1, 1);
          
          if (a === 1) throw new A.u.RandomizeError('${errorMessage}');
          var b = 1,
            c = 2,
            d = 3,
            e = 'This is a prompt',
            f = 'This is an explanation',
            g = [1, c, d];
          ({
            prompt: e,
            answer: a,
            distractors: g,
            explanation: f
          });`); //?
			}
		} catch (e) {
			error = e;
		}

		expect(result.rerollCount).toBe(10);
		expect(result.bundle).toBe(undefined);
		expect(result.errors.length).toBe(1);
		expect(result.errors).toEqual([errorMessage]);
		expect(error).toBe(undefined);
	});

	test("throws non RandomizeErrors", () => {
		let error;
		let result;

		try {
			for (let i = 0; i < 100; i += 1) {
				result = evaluate(`"use strict";

          var a = A.r.intInclusive(1, 1);
          
          if (a === 1) throw new Error('Test');
          var b = 1,
            c = 2,
            d = 3,
            e = 'This is a prompt',
            f = 'This is an explanation',
            g = [1, c, d];
          ({
            prompt: e,
            answer: a,
            distractors: g,
            explanation: f
          });`); //?
			}
		} catch (e) {
			error = e;
		}

		expect(result).toBe(undefined);
		expect(error).toBeTruthy();
		expect(error.message).toBe("Test");
	});
});
