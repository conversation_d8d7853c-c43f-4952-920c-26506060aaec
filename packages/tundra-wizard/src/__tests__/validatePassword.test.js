const { validatePassword } = require("../auth");

describe("validatePassword", () => {
	it("works", () => {
		const validPassword = "Password123!";
		const invalidPassword = "";
		const validPasswordResult = validatePassword({ password: validPassword });
		expect(validPasswordResult.errors.length).toBe(0);
		const invalidPasswordResult = validatePassword({ password: invalidPassword });
		expect(invalidPasswordResult.errors.length).toBe(5);
	});

	it("ensures passwords require capital letters", () => {
		const password = "password123!";
		const { errors } = validatePassword({ password });
		const [{ detail }] = errors;
		expect(detail).toBe("At least one capital letter is required");
	});

	it("ensures passwords require lower case letters", () => {
		const password = "PASSWORD123!";
		const { errors } = validatePassword({ password });
		const [{ detail }] = errors;
		expect(detail).toBe("At least one lower case letter is required");
	});

	it("ensures passwords require symbols", () => {
		const password = "Password1234";
		const { errors } = validatePassword({ password });
		const [{ detail }] = errors;
		expect(detail).toBe("A special character (!@#$%&?+=) is required");
	});

	it("ensures passwords require numbers", () => {
		const password = "Password!@#$";
		const { errors } = validatePassword({ password });
		const [{ detail }] = errors;
		expect(detail).toBe("At least one number is required");
	});

	it("ensures passwords require 12 or more characters", () => {
		const password = "Pass123!";
		const { errors } = validatePassword({ password });
		const [{ detail }] = errors;
		expect(detail).toBe("At least 12 characters is required");
	});
});
