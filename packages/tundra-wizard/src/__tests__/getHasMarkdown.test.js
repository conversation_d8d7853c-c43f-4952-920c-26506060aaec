const { getHasMarkdown } = require("../markdown");

describe("getHasMarkdown", () => {
	test("works", () => {
		const result = getHasMarkdown({ markdown: "<div>**Markdown**</div>" }); //?
		expect(result).toBe(true);
	});

	test("works for an empty string", () => {
		const result = getHasMarkdown({ markdown: "" }); //?
		expect(result).toBe(false);
	});

	test("works for null", () => {
		const result = getHasMarkdown({ markdown: null }); //?
		expect(result).toBe(false);
	});

	test("works for (change me)", () => {
		const result1 = getHasMarkdown({ markdown: "(change me)" }); //?
		const result2 = getHasMarkdown({ markdown: "(change me)" }); //?
		expect(result1).toBe(false);
		expect(result2).toBe(false);
	});
});
