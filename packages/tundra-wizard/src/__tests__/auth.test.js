const { canAuthor } = require("../auth");
const SCOPE = require("../scopes");

const defaultUser = {
	email: "<EMAIL>",
};

const product1 = { uuid: "product-1" };
const product2 = { uuid: "product-2" };
const content1 = { productUuid: "product-1" };
const content2 = { productUuid: "product-2" };

describe("canAuthor", () => {
	describe("granted product-1", () => {
		const grants = [
			{
				scope: SCOPE.author,
				targetType: "product",
				targetUuid: "product-1",
			},
		];

		const user = {
			...defaultUser,
			grants,
		};

		test("grant matches product", async () => {
			const result = canAuthor({ user, product: product1 });
			expect(result).toBe(true);
		});

		test("grant matches content", async () => {
			const result = canAuthor({ user, content: content1 });
			expect(result).toBe(true);
		});

		test("grant does not match product", async () => {
			const result = canAuthor({ user, product: product2 });
			expect(result).toBe(false);
		});

		test("grant does not match content", async () => {
			const result = canAuthor({ user, content: content2 });
			expect(result).toBe(false);
		});
	});
});
