const item = require("../item");
const { isCorrectNumeric } = item;

describe("isCorrectNumeric", () => {
	test("smoke", async () => {
		const input = { v: 1, s: "123" };
		const evaluated = { answer: "1234" };
		const result = isCorrectNumeric({ input, evaluated });
		expect(result).toBe(false);
	});

	describe("integers", () => {
		test("string x string", async () => {
			const input = { v: 1, s: "123" };
			const evaluated = { answer: "123" };
			const result = isCorrectNumeric({ input, evaluated });
			expect(result).toBe(true);
		});

		test("string x number", () => {
			const input = { v: 1, s: "123" };
			const evaluated = { answer: 123 };
			const result = isCorrectNumeric({ input, evaluated });
			expect(result).toBe(true);
		});

		test("string extra zeros x number", () => {
			const input = { v: 1, s: "123.00" };
			const evaluated = { answer: 123 };
			const result = isCorrectNumeric({ input, evaluated });
			expect(result).toBe(true);
		});
	});

	describe("decimals", () => {
		test("string x string", async () => {
			const input = { v: 1, s: "123.45" };
			const evaluated = { answer: "123.45" };
			const result = isCorrectNumeric({ input, evaluated });
			expect(result).toBe(true);
		});

		test("string extra zero x string", async () => {
			const input = { v: 1, s: "123.450" };
			const evaluated = { answer: "123.45" };
			const result = isCorrectNumeric({ input, evaluated });
			expect(result).toBe(true);
		});

		test("string x string extra zero", async () => {
			const input = { v: 1, s: "123.45" };
			const evaluated = { answer: "123.450" };
			const result = isCorrectNumeric({ input, evaluated });
			expect(result).toBe(true);
		});

		test("string x number", async () => {
			const input = { v: 1, s: "123.45" };
			const evaluated = { answer: 123.45 };
			const result = isCorrectNumeric({ input, evaluated });
			expect(result).toBe(true);
		});

		test("string x extra zero", async () => {
			const input = { v: 1, s: "123.45" };
			const evaluated = { answer: 123.45 };
			const result = isCorrectNumeric({ input, evaluated });
			expect(result).toBe(true);
		});
	});

	describe("commas", () => {
		test("string x string no comma", async () => {
			const input = { v: 1, s: "1,234" };
			const evaluated = { answer: "1234" };
			const result = isCorrectNumeric({ input, evaluated });
			expect(result).toBe(true);
		});

		test("string decimal x string", async () => {
			const input = { v: 1, s: "1,234.56" };
			const evaluated = { answer: "1234.56" };
			const result = isCorrectNumeric({ input, evaluated });
			expect(result).toBe(true);
		});
	});

	describe("symbols", () => {
		test("string percent x string no percent", async () => {
			const input = { v: 1, s: "77%" };
			const evaluated = { answer: "77" };
			const result = isCorrectNumeric({ input, evaluated }); //?
			expect(result).toBe(true);
		});

		test("string percent with negative sign x string no percent", async () => {
			const input = { v: 1, s: "-33.25%" };
			const evaluated = { answer: "-33.25" };
			const result = isCorrectNumeric({ input, evaluated }); //?
			expect(result).toBe(true);
		});

		test("string dollar sign x string no dollar sign", async () => {
			const input = { v: 1, s: "$24.99" };
			const evaluated = { answer: "24.99" };
			const result = isCorrectNumeric({ input, evaluated }); //?
			expect(result).toBe(true);
		});
	});
});
