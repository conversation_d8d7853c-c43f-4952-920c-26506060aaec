const PromoCodeType = require("../promo/promoCodeType");
const calculatePromoCodePrice = require("../promo/calculatePromoCodePrice");

describe("calculatePromoCodePrice", () => {
	const price = 59.99;

	test("type: discountPercent", async () => {
		const promoCode = {
			amount: 0.5,
			type: PromoCodeType.discountPercent,
		};
		const result = calculatePromoCodePrice({ price, promoCode }); //?
		expect(result).toBe(30);
	});

	test("type: discountPrice", async () => {
		const promoCode = {
			amount: 10,
			type: PromoCodeType.discountPrice,
		};
		const result = calculatePromoCodePrice({ price, promoCode }); //?
		expect(result).toBe(49.99);
	});

	test("type: price", async () => {
		const promoCode = {
			amount: 10,
			type: PromoCodeType.price,
		};
		const result = calculatePromoCodePrice({ price, promoCode }); //?
		expect(result).toBe(10);
	});

	test("type: unknown", async () => {
		const promoCode = {
			amount: 10,
			type: "dog",
		};
		const result = calculatePromoCodePrice({ price, promoCode }); //?
		expect(result).toBe(price);
	});
});
