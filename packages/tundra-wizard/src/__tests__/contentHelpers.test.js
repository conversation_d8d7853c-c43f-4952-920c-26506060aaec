const { recursivelyFlattenContent, recursivelyInflateContent, recursiveInfo } = require('../contentHelpers');

const rawContent = [
	{
		uuid: 'intro',
		slug: 'slug-intro',
		position: 100,
		name: 'Introduction',
		parentUuid: null,
	},
	{
		uuid: '1',
		slug: 'slug-1',
		position: 200,
		name: 'Options',
		parentUuid: null,
		itemsReady: 1,
		itemsAvailable: 2,
	},
	{
		uuid: '1.1',
		slug: 'slug-1.1',
		position: 100,
		name: 'Call options',
		parentUuid: '1',
		itemsReady: 10,
		itemsAvailable: 100,
	},
	{
		uuid: '2',
		slug: 'slug-2',
		position: 300,
		name: 'Municipal debt',
		parentUuid: null,
	},
	{
		uuid: '3',
		slug: 'slug-3',
		position: 400,
		name: 'Common stock',
		parentUuid: null,
	},
	{
		uuid: '3.1',
		slug: 'slug-3.1',
		position: 100,
		name: 'Rights',
		parentUuid: '3',
	},
	{
		uuid: '3.1.1',
		slug: 'slug-3.1.1',
		position: 100,
		name: 'Right to call',
		parentUuid: '3.1',
	},
	{
		uuid: 'wrapping-up',
		slug: 'slug-wrapping-up',
		position: 500,
		name: 'Wrapping up',
		parentUuid: null,
	},
];

const inflatedContent = [
	{
		uuid: 'intro',
		slug: 'slug-intro',
		position: 100,
		name: 'Introduction',
		parentUuid: null,
		key: 'content-intro',
		title: 'Introduction',
		titlePrefix: '',
		path: '/study/finra-sie/learn/slug-intro',
		content: [],
	},
	{
		uuid: '1',
		slug: 'slug-1',
		position: 200,
		name: 'Options',
		parentUuid: null,
		key: 'content-1',
		title: '1. Options',
		titlePrefix: '1.',
		path: '/study/finra-sie/learn/slug-1',
		itemsReady: 1,
		itemsAvailable: 2,
		content: [
			{
				uuid: '1.1',
				slug: 'slug-1.1',
				position: 100,
				name: 'Call options',
				parentUuid: '1',
				key: 'content-1.1',
				title: '1.1 Call options',
				titlePrefix: '1.1',
				path: '/study/finra-sie/learn/slug-1.1',
				itemsReady: 10,
				itemsAvailable: 100,
				content: [],
			},
		],
	},
	{
		uuid: '2',
		slug: 'slug-2',
		position: 300,
		name: 'Municipal debt',
		parentUuid: null,
		key: 'content-2',
		title: '2. Municipal debt',
		titlePrefix: '2.',
		path: '/study/finra-sie/learn/slug-2',
		content: [],
	},
	{
		uuid: '3',
		slug: 'slug-3',
		position: 400,
		name: 'Common stock',
		parentUuid: null,
		key: 'content-3',
		title: '3. Common stock',
		titlePrefix: '3.',
		path: '/study/finra-sie/learn/slug-3',
		content: [
			{
				uuid: '3.1',
				slug: 'slug-3.1',
				position: 100,
				name: 'Rights',
				parentUuid: '3',
				key: 'content-3.1',
				title: '3.1 Rights',
				titlePrefix: '3.1',
				path: '/study/finra-sie/learn/slug-3.1',
				content: [
					{
						uuid: '3.1.1',
						slug: 'slug-3.1.1',
						position: 100,
						name: 'Right to call',
						parentUuid: '3.1',
						key: 'content-3.1.1',
						title: '3.1.1 Right to call',
						titlePrefix: '3.1.1',
						path: '/study/finra-sie/learn/slug-3.1.1',
						content: [],
					},
				],
			},
		],
	},
	{
		uuid: 'wrapping-up',
		slug: 'slug-wrapping-up',
		position: 500,
		name: 'Wrapping up',
		parentUuid: null,
		key: 'content-wrapping-up',
		title: 'Wrapping up',
		titlePrefix: '',
		path: '/study/finra-sie/learn/slug-wrapping-up',
		content: [],
	},
];

const flattenedContentWithoutChildren = [
	{
		uuid: 'intro',
		slug: 'slug-intro',
		position: 100,
		name: 'Introduction',
		parentUuid: null,
	},
	{
		uuid: '1',
		slug: 'slug-1',
		position: 200,
		name: 'Options',
		parentUuid: null,
	},
	{
		uuid: '1.1',
		slug: 'slug-1.1',
		position: 100,
		name: 'Call options',
		parentUuid: '1',
	},
	{
		uuid: '2',
		slug: 'slug-2',
		position: 300,
		name: 'Municipal debt',
		parentUuid: null,
	},
	{
		uuid: '3',
		slug: 'slug-3',
		position: 400,
		name: 'Common stock',
		parentUuid: null,
	},
	{
		uuid: '3.1',
		slug: 'slug-3.1',
		position: 100,
		name: 'Rights',
		parentUuid: '3',
	},
	{
		uuid: '3.1.1',
		slug: 'slug-3.1.1',
		position: 100,
		name: 'Right to call',
		parentUuid: '3.1',
	},
	{
		uuid: 'wrapping-up',
		slug: 'slug-wrapping-up',
		position: 500,
		name: 'Wrapping up',
		parentUuid: null,
	},
];

describe('recursivelyInflateContent', () => {
	test('default', async () => {
		const input = {
			product: {
				slug: 'finra-sie',
			},
			content: rawContent,
			contentTitlePrefix: null,
			contentParentUuid: null,
			skipParent: true,
		};
		const result = recursivelyInflateContent(input);
		expect(result).toEqual(inflatedContent);
	});
});

describe('recursivelyFlattenContent', () => {
	test('default', async () => {
		const result = recursivelyFlattenContent({ inflatedContent });
		expect(result).toMatchObject(flattenedContentWithoutChildren);
	});
});

describe('recursiveInfo', () => {
	test('default', async () => {
		const result = recursiveInfo(inflatedContent[1]);
		expect(result).toMatchObject({
			uuids: ['1', '1.1'],
			itemsReady: 11,
			itemsAvailable: 102,
		});
	});
});
