const roundToCurrency = require("../promo/roundToCurrency");

describe("roundToCurrency", () => {
	test("exact", async () => {
		const result = roundToCurrency(59.99);
		expect(result).toBe(59.99);
	});

	test("round up", async () => {
		const result = roundToCurrency(59.999);
		expect(result).toBe(60);
	});

	test("flat", async () => {
		const result = roundToCurrency(59.0);
		expect(result).toBe(59.0);
	});
});
