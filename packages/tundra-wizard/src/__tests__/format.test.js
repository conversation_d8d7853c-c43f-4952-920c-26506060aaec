const { humanizeMs } = require("../format");

describe("humanizeMs", () => {
	test("years", async () => {
		const result = humanizeMs(57040565868);
		expect(result).toEqual("1 year, 295 days");
	});

	test("days", async () => {
		const result = humanizeMs(15452100000);
		expect(result).toEqual("178 days, 20 hours");
	});

	test("hours", async () => {
		const result = humanizeMs(52100000);
		expect(result).toEqual("14 hours, 28 minutes");
	});

	test("minutes", async () => {
		const result = humanizeMs(804000);
		expect(result).toEqual("13 minutes, 24 seconds");
	});

	test("seconds", async () => {
		const result = humanizeMs(8000);
		expect(result).toEqual("8 seconds");
	});

	test("small", async () => {
		const result = humanizeMs(593);
		expect(result).toEqual("less than a second");
	});
});
