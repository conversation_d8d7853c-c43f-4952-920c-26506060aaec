const lodash = require("lodash");
const seedrandom = require("seedrandom");
const a = require("./evaluate/A");
const { prepare } = require("./evaluate/prepare");

const NUM_REROLLS = 10;

const evaluate = (code, opts = {}) => {
	const { seed } = opts;
	seedrandom(seed, { global: true });
	const _ = lodash.runInContext(); // eslint-disable-line no-unused-vars
	const A = a(opts); // eslint-disable-line no-unused-vars

	const errorsRaw = [];
	let rerollCount = 0;
	while (rerollCount < NUM_REROLLS) {
		try {
			const bundle = eval(code); // eslint-disable-line no-eval
			return { bundle, rerollCount };
		} catch (e) {
			if (e?.code === "RANDOMIZE_ERROR") {
				rerollCount += 1;
				errorsRaw.push(e.message);
			} else {
				throw e;
			}
		}
	}

	const errors = _.uniq(errorsRaw); //?
	return { errors, rerollCount };
};

module.exports = {
	evaluate,
	prepare,
};
