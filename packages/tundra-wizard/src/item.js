const _ = require("lodash");
const numeral = require("numeral");

const isCorrectMultipleChoice = ({ input, evaluated }) => {
	const answer = _.find(evaluated.choices, (choice) => choice.correct);

	if (!answer) {
		// 20200526JP: We might be in a v0 quiz somehow.
		const result = evaluated.answer == input.s; // eslint-disable eqeqeq
		return result;
	}

	// 20200505JP: Use soft equality since v0 items could have numbers or strings as uuids.
	const result = answer.uuid == input.s; // eslint-disable eqeqeq

	return result;
};

const isCorrectSelectMultiple = ({ input, evaluated }) => {
	const { choices } = evaluated;
	const result = _.every(choices, (choice) => {
		if (choice.correct) {
			return _.includes(input.s, choice.uuid);
		}
		return !_.includes(input.s, choice.uuid);
	});
	return result;
};

const isCorrectClozeDeletion = ({ input, evaluated }) => {
	const { choices } = evaluated;
	const result = _.every(choices, (choiceSet) =>
		_.every(choiceSet, (choice) => {
			if (choice.correct) {
				return _.includes(input.s, choice.uuid);
			}
			return !_.includes(input.s, choice.uuid);
		})
	);
	return result;
};

const normalizeNumericAnswer = (raw) => {
	const regex = new RegExp("[^-0-9a-z.]", "gi");
	const stripped = raw.toString().replace(regex, "");
	const parsed = numeral(stripped).value() || "";
	return parsed.toString();
};

const isCorrectNumeric = ({ input, evaluated }) => {
	const { answer } = evaluated;
	const normalizedInput = normalizeNumericAnswer(input.s);
	const normalizedAnswer = normalizeNumericAnswer(answer);
	const result = normalizedInput === normalizedAnswer;
	return result;
};

const IS_CORRECT_ITEM_TYPE_MAP = {
	actEnglishMultipleChoice: isCorrectMultipleChoice,
	actPassageChooseOne: isCorrectMultipleChoice,
	clozeDeletion: isCorrectClozeDeletion,
	multipleChoice: isCorrectMultipleChoice,
	numeric: isCorrectNumeric,
	passageChooseMany: isCorrectSelectMultiple,
	passageChooseOne: isCorrectMultipleChoice,
	passageChooseSentence: isCorrectMultipleChoice,
	passageInsertAt: isCorrectMultipleChoice,
	quantityComparison: isCorrectMultipleChoice,
	selectMultiple: isCorrectSelectMultiple,
	sentenceEquivalence: isCorrectSelectMultiple,
};

const getIsCorrect = ({ input, evaluated, itemType }) => {
	const isCorrectFn = IS_CORRECT_ITEM_TYPE_MAP[itemType];
	const result = isCorrectFn({ input, evaluated });
	return result;
};

// 20200505JP: Handle v0 format when we didn't have proper choice objects.
const buildChoices = ({ evaluated }) => {
	const { choices, answer, distractors, itemType } = evaluated;
	if (itemType === "numeric") {
		return null;
	}

	if (choices) {
		return choices;
	}

	const answerChoice = {
		uuid: answer,
		markdown: answer,
		correct: true,
	};
	const distractorChoices = distractors.map((distractor) => ({
		uuid: distractor,
		markdown: distractor,
	}));
	const allChoices = [answerChoice, ...distractorChoices];
	const uniqChoices = _.uniqBy(allChoices, "markdown");
	return _.shuffle(uniqChoices);
};

module.exports = {
	buildChoices,
	getIsCorrect,
	isCorrectNumeric,
};
