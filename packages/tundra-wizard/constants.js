const keyMirror = require("keymirror");
const PRODUCT_FEATURES = require("./src/constants/productFeatures");
const { COURSE_LIST, COURSE_SLUGS } = require("./src/constants/courses");
const COURSE_STATS = require("./src/constants/courseStats");
const ACT_CONSTANTS = require("./src/constants/act");
const CLT_CONSTANTS = require("./src/constants/clt");
const FINRA_SIE_CONSTANTS = require("./src/constants/finra-sie");
const FINRA_SERIES_6_CONSTANTS = require("./src/constants/finra-series-6");
const FINRA_SERIES_7_CONSTANTS = require("./src/constants/finra-series-7");
const FINRA_SERIES_9_CONSTANTS = require("./src/constants/finra-series-9");
const FINRA_SERIES_63_CONSTANTS = require("./src/constants/finra-series-63");
const FINRA_SERIES_65_CONSTANTS = require("./src/constants/finra-series-65");
const FINRA_SERIES_66_CONSTANTS = require("./src/constants/finra-series-66");
const GRE_CONSTANTS = require("./src/constants/gre");
const INSURANCE_LIFE_HEALTH_CONSTANTS = require("./src/constants/insurance-life-health");
const INSURANCE_PROPERTY_CASUALTY_CONSTANTS = require("./src/constants/insurance-property-casualty");
const INSURANCE_PERSONAL_LINES_CONSTANTS = require("./src/constants/insurance-personal-lines");
const INSURANCE_LIFE_CONSTANTS = require("./src/constants/insurance-life");
const INSURANCE_HEALTH_CONSTANTS = require("./src/constants/insurance-health");
const GROUP_VIEW_TYPES = require("./src/constants/organizations/groupViewTypes");
const { US_STATES, US_STATES_BY_VARIANT, US_STATES_VARIANT_ENUM } = require("./src/constants/usStates");

const EXAM_TIME_REVIEW_PERCENT = 0.5;

const EXAM_WITH_REVIEW_TIME_MULTIPLIER = 1.2;

const INSIGHT_TYPE = keyMirror({
	consolidatedSummary: null,
	timeUsage: null,
	troublesomeQuestions: null,
});

const ITEM_NAME_MAPPING = {
	act: {
		"act--english--punctuation": "Punctuation",
		"act--english--grammar-and-usage": "Grammar and usage",
		"act--english--sentence-structure": "Sentence structure",
		"act--english--vocab": "Vocabulary",
		"act--english--passage-organization": "Passage organization",
		"act--english--unity-and-cohesion": "Unity and cohesion",
		"act--english--rhetorical-skills": "Rhetorical skills",
		"act--reading": "Reading skills",
		"act--science": "Science skills",
	},
	gre: {
		"gre--readingComprehension": "Reading comprehension",
		"gre--sentenceEquivalence": "Sentence equivalence",
		"gre--textCompletion": "Text completion",
		"gre--logicalReasoning": "Logical reasoning",
	},
	"gre-v2": {
		"gre-v2--readingComprehension": "Reading comprehension",
		"gre-v2--sentenceEquivalence": "Sentence equivalence",
		"gre-v2--textCompletion": "Text completion",
		"gre-v2--logicalReasoning": "Logical reasoning",
	},
};

const PRESENTATION_MAX_TIME_MS_LIMIT = 20 * 60 * 1000;
const READING_MAX_TIME_MS_LIMIT = 60 * 60 * 1000;

const PRODUCTS_JSON_SHORTHAND_MAP = {
	finra: [
		"finra-sie",
		"finra-series-6",
		"finra-series-7",
		"finra-series-9",
		"finra-series-63",
		"finra-series-65",
		"finra-series-66",
		"finra-bundle-sie-s7",
		"finra-everything",
	],
	insurance: [
		"insurance-life-health",
		"insurance-life",
		"insurance-health",
		// 'insurance-personal-lines',
		// 'insurance-property-casualty',
	],
	university: ["amc-12", "gre-v2", "finra-sie"],
};

const PRODUCT_CONSTANTS = {
	act: ACT_CONSTANTS,
	clt: CLT_CONSTANTS,
	"finra-sie": FINRA_SIE_CONSTANTS,
	"finra-series-6": FINRA_SERIES_6_CONSTANTS,
	"finra-series-7": FINRA_SERIES_7_CONSTANTS,
	"finra-series-9": FINRA_SERIES_9_CONSTANTS,
	"finra-series-63": FINRA_SERIES_63_CONSTANTS,
	"finra-series-65": FINRA_SERIES_65_CONSTANTS,
	"finra-series-66": FINRA_SERIES_66_CONSTANTS,
	"gre-v2": GRE_CONSTANTS,
	"insurance-life-health": INSURANCE_LIFE_HEALTH_CONSTANTS,
	"insurance-life": INSURANCE_LIFE_CONSTANTS,
	"insurance-health": INSURANCE_HEALTH_CONSTANTS,
	"insurance-property-casualty": INSURANCE_PROPERTY_CASUALTY_CONSTANTS,
	"insurance-personal-lines": INSURANCE_PERSONAL_LINES_CONSTANTS,
};

const PRODUCT_SLUG_MIN_SCORES = {
	default: 0.6,
	"finra-sie": {
		default: 0.7,
	},
	"finra-series-6": {
		default: 0.7,
	},
	"finra-series-7": {
		default: 0.72,
	},
	"finra-series-9": {
		default: 0.7,
	},
	"finra-series-10": {
		default: 0.7,
	},
	"finra-series-24": {
		default: 0.7,
	},
	"finra-series-63": {
		default: 43 / 60,
	},
	"finra-series-65": {
		default: 92 / 130,
	},
	"finra-series-66": {
		default: 0.73,
	},
};

const PRODUCTS_WITH_EXAM_READINESS = [
	"finra-sie",
	"finra-series-6",
	"finra-series-7",
	"finra-series-9",
	"finra-series-63",
	"finra-series-65",
	"finra-series-66",
];

const REVIEW_QUESTION_MULTIPLIERS_MS = {
	act: 30 * 1000,
	clt: 30 * 1000,
	"amc-8": 30 * 1000,
	"amc-12": 30 * 1000,
	"finra-sie": 30 * 1000,
	"finra-series-6": 30 * 1000,
	"finra-series-7": 30 * 1000,
	"finra-series-9": 30 * 1000,
	"finra-series-63": 30 * 1000,
	"finra-series-65": 30 * 1000,
	"finra-series-66": 30 * 1000,
	gre: 120 * 1000,
	"gre-v2": 120 * 1000,
	"insurance-life-health": 100 * 1000,
	"insurance-life": 30 * 1000,
	"insurance-health": 30 * 1000,
	"insurance-property-casualty": 30 * 1000,
	"insurance-personal-lines": 30 * 1000,
	"usmle-step-1": 30 * 1000,
	default: 30 * 1000,
};

const TIMEZONE_UTC_OFFSET_MAP = {
	edt: "-04:00",
	est: "-05:00",
	cdt: "-05:00",
	cst: "-06:00",
	mdt: "-06:00",
	mst: "-07:00",
	pdt: "-07:00",
	pst: "-08:00",
	utc: "+00:00",
};

module.exports = {
	COURSE_LIST,
	COURSE_SLUGS,
	COURSE_STATS,
	EXAM_TIME_REVIEW_PERCENT,
	EXAM_WITH_REVIEW_TIME_MULTIPLIER,
	GROUP_VIEW_TYPES,
	INSIGHT_TYPE,
	ITEM_NAME_MAPPING,
	PRESENTATION_MAX_TIME_MS_LIMIT,
	PRODUCT_CONSTANTS,
	PRODUCT_FEATURES,
	PRODUCT_SLUG_MIN_SCORES,
	PRODUCTS_JSON_SHORTHAND_MAP,
	PRODUCTS_WITH_EXAM_READINESS,
	READING_MAX_TIME_MS_LIMIT,
	REVIEW_QUESTION_MULTIPLIERS_MS,
	TIMEZONE_UTC_OFFSET_MAP,
	US_STATES_BY_VARIANT,
	US_STATES_VARIANT_ENUM,
	US_STATES,
};
