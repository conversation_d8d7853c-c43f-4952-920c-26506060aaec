const _ = require("lodash");

const getAggregatedStudyTimeBreakdown = ({ studyTimeBreakdown }) => {
	const studyTimeByDay = _.groupBy(studyTimeBreakdown, "date");
	const aggregated = _.map(studyTimeByDay, (day, date) => {
		const tasksByType = _.groupBy(day, "type");
		const totalReadingTimeMs = _.sumBy(tasksByType.reading, "totalMs");
		const totalExamTimeMs = _.sumBy(tasksByType.exam, "totalMs");
		const totalQuizTimeMs = _.sumBy(tasksByType.quiz, "totalMs");
		const totalItemCount = _.sumBy(tasksByType.total, "data.itemCount");
		const totalTimeMs = _.sumBy(tasksByType.total, "totalMs");
		const totalStudyTimeMs = totalReadingTimeMs + totalExamTimeMs + totalQuizTimeMs;
		const totalUnproductiveTimeMs = totalTimeMs - totalStudyTimeMs;

		return {
			date,
			totalTimeMs,
			totalItemCount,
			totalStudyTimeMs,
			totalReadingTimeMs,
			totalExamTimeMs,
			totalQuizTimeMs,
			totalUnproductiveTimeMs,
		};
	});
	const studyTimeByDayAggregated = _.sortBy(aggregated, "date");
	const totalTimeMs = _.sumBy(studyTimeByDayAggregated, "totalTimeMs");
	const totalStudyTimeMs = _.sumBy(studyTimeByDayAggregated, "totalStudyTimeMs");
	const totalReadingTimeMs = _.sumBy(studyTimeByDayAggregated, "totalReadingTimeMs");
	const totalExamTimeMs = _.sumBy(studyTimeByDayAggregated, "totalExamTimeMs");
	const totalQuizTimeMs = _.sumBy(studyTimeByDayAggregated, "totalQuizTimeMs");
	const totalItemCount = _.sumBy(studyTimeByDayAggregated, "totalItemCount");
	const totalUnproductiveTimeMs = _.sumBy(studyTimeByDayAggregated, "totalUnproductiveTimeMs");

	return {
		totalTimeMs,
		totalItemCount,
		totalStudyTimeMs,
		totalReadingTimeMs,
		totalExamTimeMs,
		totalQuizTimeMs,
		totalUnproductiveTimeMs,
		studyTimeByDay,
		studyTimeByDayAggregated,
	};
};

module.exports = {
	getAggregatedStudyTimeBreakdown,
};
