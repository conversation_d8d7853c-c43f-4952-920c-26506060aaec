import eslintPluginJest from "eslint-plugin-jest";
import globals from "globals";

import baseConfig from "../../eslint.config.mjs";

export default [
	...baseConfig,

	{
		files: ["**/*.{js,ts}"],
		plugins: {
			jest: eslintPluginJest,
		},
		rules: {
			"@typescript-eslint/no-shadow": "warn",
		},
		languageOptions: {
			globals: {
				...globals.node,
				...eslintPluginJest.environments.globals.globals,
			},
		},
	},
];
