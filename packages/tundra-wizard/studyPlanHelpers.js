const _ = require("lodash");
const dayjs = require("./dayjs");
const { msToMinRaw } = require("./format");
const { EXAM_WITH_REVIEW_TIME_MULTIPLIER, PRODUCT_CONSTANTS, REVIEW_QUESTION_MULTIPLIERS_MS } = require("./constants");
const { FINRA_SIE_EXAMS_BY_VARIANT } = require("./src/constants/finra-sie/exams");
const { FINRA_SERIES_6_EXAMS_BY_VARIANT } = require("./src/constants/finra-series-6/exams");
const { FINRA_SERIES_7_EXAMS_BY_VARIANT } = require("./src/constants/finra-series-7/exams");
const { GRE_EXAMS_BY_VARIANT } = require("./src/constants/gre/exams");

const addReviewDaysToPlan = ({ plan, examDay }) => {
	const lastStudyDay = _.get(plan, [plan.length - 1, "day"]);

	const updatedPlan = _.reduce(
		plan,
		(acc, planDay, i) => {
			const prevPlanDay = plan[i - 1];

			if (!prevPlanDay) return acc.concat(planDay);

			const { day } = planDay;
			const isLastStudyDay = day === lastStudyDay;
			const hasGapDays = dayjs(day).diff(dayjs(prevPlanDay.day), "day") > 1;
			const fillToExamDay = !hasGapDays && isLastStudyDay;
			const startDay = fillToExamDay ? day : prevPlanDay.day;
			const finishDay = fillToExamDay ? examDay : day;
			const reviewDays = [];

			let reviewDay = dayjs(startDay).add(1, "day").format("YYYY-MM-DD");
			while (reviewDay < finishDay) {
				reviewDays.push({
					day: reviewDay,
					read: [],
					exams: [],
					tasks: ["review"],
				});
				reviewDay = dayjs(reviewDay).add(1, "day").format("YYYY-MM-DD");
			}

			if (hasGapDays) {
				acc.push(...reviewDays, planDay);
			} else {
				acc.push(planDay, ...reviewDays);
			}

			return acc;
		},
		[]
	);

	return updatedPlan;
};

const addSpecialEventsToPlan = ({ hasFeedbackEnabled, plan, examDay }) => {
	const updatedPlan = addReviewDaysToPlan({ plan, examDay });

	if (!hasFeedbackEnabled) return updatedPlan;

	const sleepDayString = dayjs(examDay).subtract(1, "day").format("YYYY-MM-DD");
	const sleepDayPlan = _.find(updatedPlan, (planDay) => planDay.day === sleepDayString);
	if (sleepDayPlan) {
		sleepDayPlan.tasks = ["sleep"];
	} else {
		updatedPlan.push({
			day: sleepDayString,
			tasks: ["sleep"],
		});
	}

	return updatedPlan;
};

const getCombinedUserExams = ({ essayResponses, productSlug, userExams }) => {
	const finishedEssays = _.filter(essayResponses, (essay) => essay.submitAt);
	const mappedFinishedEssays = _.map(finishedEssays, (essay) => {
		const {
			submitAt,
			essayPrompt: { type },
		} = essay;

		return { ...essay, finishedAt: submitAt, variant: type };
	});

	const finishedExams = userExams.filter((exam) => exam.finishedAt);
	const mappedFinishedExams = finishedExams.map((exam) => {
		const { variant: variantRaw } = exam;
		const examsByVariant = getExamsByVariant({ productSlug });

		const variant = (() => {
			if (!variantRaw) return "exam";
			if (variantRaw.includes("quant")) return "quant";
			if (variantRaw.includes("verbal")) return "verbal";

			const examConstant = _.get(examsByVariant, variantRaw);
			const examVariant = _.get(examConstant, "variant");
			if (examVariant) return examVariant;

			return variantRaw;
		})();

		return { ...exam, variant };
	});

	const combinedExams = _.concat([], mappedFinishedEssays, mappedFinishedExams);
	const sortedCombinedExams = _.sortBy(combinedExams, ["finishedAt"]);

	return sortedCombinedExams;
};

const annotatePlanWithCompletedAt = ({ essayResponses = [], plan, productSlug, contentByUuid, userExams }) => {
	const allUserExams = getCombinedUserExams({ essayResponses, productSlug, userExams });
	const userExamsByVariant = _.groupBy(allUserExams, "variant");
	const examsByVariant = getExamsByVariant({ productSlug });

	const annotatedPlan = _.map(plan, (planDay) => {
		const { day, read = [], exams = [] } = planDay;

		const annotatedRead = _.map(read, (contentUuid) => {
			const completedAt = _.get(contentByUuid, [contentUuid, "completedAt"]);
			return {
				contentUuid,
				completedAt,
			};
		});

		const annotatedExams = _.map(exams, (examType) => {
			const userExams = (() => {
				const userExamsForExamType = _.get(userExamsByVariant, examType, []);
				const variant = _.get(examsByVariant, [examType, "variant"]);
				const isAltVariant = examType !== variant;

				if (!isAltVariant) return userExamsForExamType;

				//20231201JF: this ensures user exams for finra variants that have changed are still marked complete, e.g. "primary-market" and "the-primary-market"
				const userExamsForVariant = _.get(userExamsByVariant, variant, []);
				userExamsForExamType.push(...userExamsForVariant);
				return userExamsForExamType;
			})();

			//20231201JF: full-length exams do not get marked complete if taken before their scheduled date
			const isFinra = productSlug.includes("finra");
			if (isFinra && examType === "exam") {
				_.remove(userExams, ({ finishedAt }) => dayjs(finishedAt).format("YYYY-MM-DD") < day);
			}

			const userExam = userExams.shift();

			if (!userExam) return { examType, completedAt: null };

			const { finishedAt: completedAt } = userExam;
			return { examType, completedAt };
		});

		return {
			day,
			read: annotatedRead,
			exams: annotatedExams,
		};
	});

	return annotatedPlan;
};

const annotateReadableContentWithTotalTime = ({ contentByUuid, productSlug, readableContent }) => {
	const readableContentWithTotalTime = _.map(readableContent, (item) => {
		const { contentUuid } = item;
		const contentInstance = contentByUuid[contentUuid];

		const { numItems, readingTimeMs } = contentInstance;
		const reviewQuestionTimeMs = REVIEW_QUESTION_MULTIPLIERS_MS[productSlug] || REVIEW_QUESTION_MULTIPLIERS_MS.default;
		const totalReviewQuestionTimeMs = numItems * reviewQuestionTimeMs;
		const totalTimeMsRaw = readingTimeMs + totalReviewQuestionTimeMs;
		const totalTimeMin = msToMinRaw(totalTimeMsRaw);

		return {
			...item,
			totalTimeMin,
		};
	});

	return readableContentWithTotalTime;
};

const getExamsByVariant = ({ productSlug }) => {
	const productExams = _.get(PRODUCT_CONSTANTS, [productSlug, "exams"]);
	const defaultExamsByVariant = _.keyBy(productExams, "variant");

	const examsByVariant = (() => {
		switch (productSlug) {
			case "finra-sie":
				return FINRA_SIE_EXAMS_BY_VARIANT;
			case "finra-series-6":
				return FINRA_SERIES_6_EXAMS_BY_VARIANT;
			case "finra-series-7":
				return FINRA_SERIES_7_EXAMS_BY_VARIANT;
			case "gre":
			case "gre-v2":
				return GRE_EXAMS_BY_VARIANT;
			default:
				return defaultExamsByVariant;
		}
	})();

	return examsByVariant;
};

const annotateExamsWithTotalTime = ({ exams, productSlug }) => {
	const productExams = _.get(PRODUCT_CONSTANTS, [productSlug, "exams"]);

	if (!productExams) return [];

	const examsByVariant = getExamsByVariant({ productSlug });

	if (productSlug === "gre-v2") {
		examsByVariant["quant"] = { allottedTimeMins: 23.5 };
		examsByVariant["verbal"] = { allottedTimeMins: 20.5 };
	}

	const examsWithTotalTime = _.map(exams, (exam) => {
		const { examType } = exam;
		const examVariant = examsByVariant[examType];
		const { allottedTimeMins } = examVariant;
		const totalTimeMin = Math.ceil(allottedTimeMins * EXAM_WITH_REVIEW_TIME_MULTIPLIER);

		return {
			...exam,
			totalTimeMin,
		};
	});

	return examsWithTotalTime;
};

const calculateStudyScore = ({ essayResponses, plan, productSlug, contentByUuid, userExams }) => {
	const annotatedPlan = annotatePlanWithCompletedAt({
		essayResponses,
		plan,
		productSlug,
		contentByUuid,
		userExams,
	});

	const { overdue, upcoming } = segmentPlanByTaskStatus({
		plan: annotatedPlan,
	});

	const totalOverdueStudyTimeMin = getTotalOverdueStudyTimeMin({
		contentByUuid,
		overdue,
		productSlug,
	});
	const totalUpcomingStudyTimeMin = getTotalUpcomingStudyTimeMin({
		contentByUuid,
		productSlug,
		upcoming,
	});

	const percentOverdue = totalOverdueStudyTimeMin / (totalOverdueStudyTimeMin + totalUpcomingStudyTimeMin);
	const studyScore = 1 - percentOverdue;

	return studyScore;
};

// 20220329JP: Kind of a hack to filter out unreadable parent nodes
const isReadable = (content) => content && !content.content.length;

const filterReadableContent = ({ contentByUuid, read }) => {
	if (!read) {
		return [];
	}

	const filtered = _.filter(read, (item) => {
		const { contentUuid } = item;
		const contentInstance = contentByUuid[contentUuid];
		return isReadable(contentInstance);
	});

	return filtered;
};

const getTotalStudyTimeMin = ({ contentByUuid, exams, productSlug, read }) => {
	const readableContent = filterReadableContent({
		contentByUuid,
		read,
	});
	const readableContentWithTotalTime = annotateReadableContentWithTotalTime({
		contentByUuid,
		productSlug,
		readableContent: readableContent,
	});
	const examsWithTotalTime = annotateExamsWithTotalTime({
		exams,
		productSlug,
	});

	const totalStudyTimeMin =
		_.sumBy(readableContentWithTotalTime, "totalTimeMin") + _.sumBy(examsWithTotalTime, "totalTimeMin");

	return totalStudyTimeMin;
};

const getTotalOverdueStudyTimeMin = ({ contentByUuid, overdue, productSlug }) => {
	const overdueExams = _.get(overdue, "exams") || [];
	const overdueRead = _.get(overdue, "read") || [];

	const totalOverdueStudyTimeMin = getTotalStudyTimeMin({
		contentByUuid,
		exams: overdueExams,
		productSlug,
		read: overdueRead,
	});

	return totalOverdueStudyTimeMin;
};

const getTotalUpcomingStudyTimeMin = ({ contentByUuid, productSlug, upcoming }) => {
	const totalUpcomingStudyTimeMin = _.reduce(
		upcoming,
		(acc, dayEntry) => {
			const { read, exams } = dayEntry;
			const totalStudyTimeMin = getTotalStudyTimeMin({
				contentByUuid,
				exams,
				productSlug,
				read,
			});

			acc += totalStudyTimeMin;
			return acc;
		},
		0
	);

	return totalUpcomingStudyTimeMin;
};

const segmentPlanByTaskStatus = ({ plan }) => {
	const todayString = dayjs().format("YYYY-MM-DD");

	const overdueRead = [];
	const overdueExams = [];

	const upcoming = [];

	for (let i = 0; i < plan.length; i += 1) {
		const planDay = plan[i];
		const { day, read = [], exams = [] } = planDay;

		if (day < todayString) {
			_.forEach(read, (task) => {
				const { completedAt } = task;
				if (!completedAt) {
					overdueRead.push(task);
				}
			});
			_.forEach(exams, (task) => {
				const { completedAt } = task;
				if (!completedAt) {
					overdueExams.push(task);
				}
			});
		} else {
			upcoming.push(planDay);
		}
	}

	const hasOverdueContent = overdueRead.length > 0 || overdueExams.length > 0;
	const overdue = hasOverdueContent ? { read: overdueRead, exams: overdueExams } : null;

	return {
		upcoming,
		overdue,
	};
};

const SUCCESS_CUTOFF = 0.9;
const AT_RISK_CUTOFF = 0.75;

const getStudyStatus = ({ examDay, status, studyScore }) => {
	switch (status) {
		case "positive": {
			return {
				theme: "success",
				text: "Passed",
			};
		}
		case "negative": {
			return {
				theme: "error",
				text: "Failed",
			};
		}
		case "feedback": {
			return {
				theme: null,
				text: "Pending",
			};
		}
		default:
			break;
	}

	if (studyScore == null || examDay <= dayjs().format("YYYY-MM-DD")) {
		return {
			theme: null,
			text: "Pending",
		};
	}

	if (studyScore > SUCCESS_CUTOFF) {
		return {
			theme: "success",
			text: "On track",
		};
	}

	if (studyScore >= AT_RISK_CUTOFF) {
		return {
			theme: "warn",
			text: "Off track",
		};
	}

	return {
		theme: "error",
		text: "At risk",
	};
};

const DEFAULT_STUDY_PLAN_LENGTH = 60;

const getDefaultExamDayMoment = ({ examDay = "", productSlug = "" }) => {
	const plannedExamDate = examDay ? dayjs(examDay) : undefined;
	const currentDate = dayjs();

	if (plannedExamDate && plannedExamDate.isAfter(currentDate)) {
		return plannedExamDate;
	}

	const constants = PRODUCT_CONSTANTS[productSlug];
	const studyPlanLength = _.get(constants, "studyPlanOptions.numDays", DEFAULT_STUDY_PLAN_LENGTH);

	const result = currentDate.add(studyPlanLength, "day");
	return result;
};

module.exports = {
	addSpecialEventsToPlan,
	annotatePlanWithCompletedAt,
	annotateReadableContentWithTotalTime,
	annotateExamsWithTotalTime,
	calculateStudyScore,
	filterReadableContent,
	getDefaultExamDayMoment,
	getExamsByVariant,
	getStudyStatus,
	getTotalStudyTimeMin,
	getTotalOverdueStudyTimeMin,
	getTotalUpcomingStudyTimeMin,
	segmentPlanByTaskStatus,
};
