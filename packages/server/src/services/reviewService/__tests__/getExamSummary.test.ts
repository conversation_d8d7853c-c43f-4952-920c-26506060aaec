import * as Sentry from '@sentry/node';
import { getExamSummary } from '../reviewService';
import getVariantExamSummary from '../getVariantExamSummary';
import getEnumeratedExamSummary from '../getEnumeratedExamSummary';
import getCategorizedExamSummary from '../getCategorizedExamSummary';
import getFinraExamSummary from '../getFinraExamSummary';

// Mock dependencies
jest.mock('../getVariantExamSummary', () => jest.fn());
jest.mock('../getEnumeratedExamSummary', () => jest.fn());
jest.mock('../getCategorizedExamSummary', () => jest.fn());
jest.mock('../getFinraExamSummary', () => jest.fn());
jest.mock('@sentry/node', () => ({
  captureMessage: jest.fn(),
}));

describe('getExamSummary', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call the correct function for ACT math exam', async () => {
    getVariantExamSummary.mockResolvedValue([]);
    
    await getExamSummary({
      period: '7d',
      productSlug: 'act',
      variant: 'math',
      userUuid: 'user-123',
    });
    
    expect(getVariantExamSummary).toHaveBeenCalledWith({
      period: '7d',
      productSlug: 'act',
      userExamUuid: undefined,
      userUuid: 'user-123',
      variant: 'math',
    });
  });

  it('should call the correct function for ACT reading exam', async () => {
    getCategorizedExamSummary.mockResolvedValue([]);
    
    await getExamSummary({
      period: '7d',
      productSlug: 'act',
      variant: 'reading',
      userUuid: 'user-123',
    });
    
    expect(getCategorizedExamSummary).toHaveBeenCalledWith({
      period: '7d',
      productSlug: 'act',
      userExamUuid: undefined,
      userUuid: 'user-123',
      variant: 'reading',
    });
  });

  it('should call the correct function for FINRA SIE exam', async () => {
    getFinraExamSummary.mockResolvedValue([]);
    
    await getExamSummary({
      period: '7d',
      productSlug: 'finra-sie',
      variant: 'default',
      userUuid: 'user-123',
    });
    
    expect(getFinraExamSummary).toHaveBeenCalledWith({
      period: '7d',
      productSlug: 'finra-sie',
      userExamUuid: undefined,
      userUuid: 'user-123',
      variant: null,
    });
  });

  it('should handle variant=default by setting variant to null', async () => {
    getFinraExamSummary.mockResolvedValue([]);
    
    await getExamSummary({
      period: '7d',
      productSlug: 'finra-series-7',
      variant: 'default',
      userUuid: 'user-123',
    });
    
    expect(getFinraExamSummary).toHaveBeenCalledWith({
      period: '7d',
      productSlug: 'finra-series-7',
      userExamUuid: undefined,
      userUuid: 'user-123',
      variant: null,
    });
  });

  it('should pass userExamUuid when provided', async () => {
    getFinraExamSummary.mockResolvedValue([]);
    
    await getExamSummary({
      period: '7d',
      productSlug: 'finra-series-7',
      variant: 'default',
      userUuid: 'user-123',
      userExamUuid: 'exam-456',
    });
    
    expect(getFinraExamSummary).toHaveBeenCalledWith({
      period: '7d',
      productSlug: 'finra-series-7',
      userExamUuid: 'exam-456',
      userUuid: 'user-123',
      variant: null,
    });
  });

  it('should log to Sentry when no examSummaryFn is found', async () => {
    const result = await getExamSummary({
      period: '7d',
      productSlug: 'unknown-exam',
      variant: 'default',
      userUuid: 'user-123',
    });
    
    expect(Sentry.captureMessage).toHaveBeenCalledWith(
      'examSummaryFn not found for unknown-exam',
      expect.any(Object)
    );
    expect(result).toEqual([]);
  });
});