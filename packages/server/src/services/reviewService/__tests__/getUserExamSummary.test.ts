import getUserExamSummary from '../getUserExamSummary';

describe('getUserExamSummary', () => {
  it('should correctly group items by content UUID', () => {
    const userExamItems = [
      { contentUuid: 'content-1', contentName: 'Content 1', position: 1, score: 1 },
      { contentUuid: 'content-1', contentName: 'Content 1', position: 1, score: 0 },
      { contentUuid: 'content-2', contentName: 'Content 2', position: 2, score: 1 }
    ];

    const result = getUserExamSummary({ period: '7d', userExamItems });
    
    expect(result).toEqual([
      { uuid: 'content-1--7d', name: 'Content 1', position: 1, total: 2, totalCorrect: 1 },
      { uuid: 'content-2--7d', name: 'Content 2', position: 2, total: 1, totalCorrect: 1 }
    ]);
  });

  it('should include userExamUuid in the uuid when provided', () => {
    const userExamItems = [
      { contentUuid: 'content-1', contentName: 'Content 1', position: 1, score: 1 }
    ];

    const result = getUserExamSummary({ 
      period: '7d', 
      userExamUuid: 'exam-123', 
      userExamItems 
    });
    
    expect(result[0].uuid).toBe('exam-123--content-1--7d');
  });

  it('should include variant in the uuid when provided', () => {
    const userExamItems = [
      { contentUuid: 'content-1', contentName: 'Content 1', position: 1, score: 1 }
    ];

    const result = getUserExamSummary({ 
      period: '7d', 
      userExamItems,
      variant: 'math' 
    });
    
    expect(result[0].uuid).toBe('content-1--7d--math');
  });

  it('should handle items with no position', () => {
    const userExamItems = [
      { contentUuid: 'content-1', contentName: 'Content 1', score: 1 },
      { contentUuid: 'content-2', contentName: 'Content 2', score: 0 }
    ];

    const result = getUserExamSummary({ period: '7d', userExamItems });
    
    expect(result).toEqual([
      { uuid: 'content-1--7d', name: 'Content 1', position: null, total: 1, totalCorrect: 1 },
      { uuid: 'content-2--7d', name: 'Content 2', position: null, total: 1, totalCorrect: 0 }
    ]);
  });

  it('should handle empty userExamItems array', () => {
    const result = getUserExamSummary({ period: '7d', userExamItems: [] });
    expect(result).toEqual([]);
  });

  it('should sort results by position', () => {
    const userExamItems = [
      { contentUuid: 'content-2', contentName: 'Content 2', position: 2, score: 1 },
      { contentUuid: 'content-1', contentName: 'Content 1', position: 1, score: 0 },
      { contentUuid: 'content-3', contentName: 'Content 3', position: 3, score: 1 }
    ];

    const result = getUserExamSummary({ period: '7d', userExamItems });
    
    expect(result.map(item => item.position)).toEqual([1, 2, 3]);
  });
});