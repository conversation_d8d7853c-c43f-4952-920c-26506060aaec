import reduceExternalExamSummaryResults from '../reduceExternalExamSummaryResults';

describe('reduceExternalExamSummaryResults', () => {
  it('should correctly reduce results with single category', () => {
    const results = [
      { categoryUuid: 'category-1', categoryName: 'Category 1', status: 'correct', count: 3 },
      { categoryUuid: 'category-1', categoryName: 'Category 1', status: 'incorrect', count: 2 }
    ];

    const summary = reduceExternalExamSummaryResults({ results });
    
    expect(summary).toEqual([
      { categoryUuid: 'category-1', categoryName: 'Category 1', correct: 3, total: 5 }
    ]);
  });

  it('should correctly reduce results with multiple categories', () => {
    const results = [
      { categoryUuid: 'category-1', categoryName: 'Category 1', status: 'correct', count: 3 },
      { categoryUuid: 'category-1', categoryName: 'Category 1', status: 'incorrect', count: 2 },
      { categoryUuid: 'category-2', categoryName: 'Category 2', status: 'correct', count: 1 },
      { categoryUuid: 'category-2', categoryName: 'Category 2', status: 'incorrect', count: 4 }
    ];

    const summary = reduceExternalExamSummaryResults({ results });
    
    expect(summary).toEqual([
      { categoryUuid: 'category-1', categoryName: 'Category 1', correct: 3, total: 5 },
      { categoryUuid: 'category-2', categoryName: 'Category 2', correct: 1, total: 5 }
    ]);
  });

  it('should handle empty results array', () => {
    const results = [];
    const summary = reduceExternalExamSummaryResults({ results });
    expect(summary).toEqual([]);
  });

  it('should handle results with no correct answers', () => {
    const results = [
      { categoryUuid: 'category-1', categoryName: 'Category 1', status: 'incorrect', count: 5 }
    ];

    const summary = reduceExternalExamSummaryResults({ results });
    
    expect(summary).toEqual([
      { categoryUuid: 'category-1', categoryName: 'Category 1', correct: 0, total: 5 }
    ]);
  });

  it('should handle results with all correct answers', () => {
    const results = [
      { categoryUuid: 'category-1', categoryName: 'Category 1', status: 'correct', count: 5 }
    ];

    const summary = reduceExternalExamSummaryResults({ results });
    
    expect(summary).toEqual([
      { categoryUuid: 'category-1', categoryName: 'Category 1', correct: 5, total: 5 }
    ]);
  });

  it('should handle results with multiple statuses', () => {
    const results = [
      { categoryUuid: 'category-1', categoryName: 'Category 1', status: 'correct', count: 3 },
      { categoryUuid: 'category-1', categoryName: 'Category 1', status: 'incorrect', count: 2 },
      { categoryUuid: 'category-1', categoryName: 'Category 1', status: 'skipped', count: 1 }
    ];

    const summary = reduceExternalExamSummaryResults({ results });
    
    expect(summary).toEqual([
      { categoryUuid: 'category-1', categoryName: 'Category 1', correct: 3, total: 6 }
    ]);
  });
});