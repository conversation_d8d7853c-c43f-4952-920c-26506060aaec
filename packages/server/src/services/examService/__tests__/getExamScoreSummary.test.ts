import * as Sentry from '@sentry/node';

import { dbWriter } from '$daos';

import { getActMathSummary } from '../actExamScoreSummary';
import getCategorizedExamSummary from '../getCategorizedExamSummary';
import getEnumeratedExamSummary from '../getEnumeratedExamSummary';
import { getExamScoreSummary } from '../getExamScoreSummary';
import getGreQuantSummary from '../getGreQuantSummary';

// Mock dependencies
jest.mock('$daos', () => ({
	dbWriter: {
		query: jest.fn(),
	},
	dbWriterModels: {
		UserExam: {
			findOne: jest.fn(),
		},
	},
}));

jest.mock('../actExamScoreSummary', () => ({
	getActMathSummary: jest.fn(),
}));

jest.mock('../getCategorizedExamSummary', () => jest.fn());
jest.mock('../getEnumeratedExamSummary', () => jest.fn());
jest.mock('../getGreQuantSummary', () => jest.fn());
jest.mock('@sentry/node', () => ({
	captureMessage: jest.fn(),
}));

describe('getExamScoreSummary', () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	it('should call the correct function for ACT math exam', async () => {
		const userExam = {
			uuid: 'exam-123',
			productSlug: 'act',
			variant: 'math',
		};

		await getGroupedResults({ userExam });

		expect(getActMathSummary).toHaveBeenCalledWith({ userExam });
	});

	it('should call the correct function for ACT reading exam', async () => {
		const userExam = {
			uuid: 'exam-123',
			productSlug: 'act',
			variant: 'reading',
		};

		await getGroupedResults({ userExam });

		expect(getCategorizedExamSummary).toHaveBeenCalledWith({ userExam });
	});

	it('should call the correct function for GRE verbal exam', async () => {
		const userExam = {
			uuid: 'exam-123',
			productSlug: 'gre',
			variant: 'verbal',
		};

		await getGroupedResults({ userExam });

		expect(getEnumeratedExamSummary).toHaveBeenCalledWith({ userExam });
	});

	it('should call the correct function for GRE quant exam', async () => {
		const userExam = {
			uuid: 'exam-123',
			productSlug: 'gre',
			variant: 'quant',
		};

		await getGroupedResults({ userExam });

		expect(getGreQuantSummary).toHaveBeenCalledWith({ userExam });
	});

	it('should call the correct function for GRE-V2 quant-short exam', async () => {
		const userExam = {
			uuid: 'exam-123',
			productSlug: 'gre-v2',
			variant: 'quant-short',
		};

		await getGroupedResults({ userExam });

		expect(getGreQuantSummary).toHaveBeenCalledWith({ userExam });
	});

	it('should call the default function for AMC-12 exam', async () => {
		const userExam = {
			uuid: 'exam-123',
			productSlug: 'amc-12',
			variant: 'any-variant',
		};

		await getGroupedResults({ userExam });

		expect(getEnumeratedExamSummary).toHaveBeenCalledWith({ userExam });
	});

	it('should fall back to standard summary for unknown exam types', async () => {
		const userExam = {
			uuid: 'exam-123',
			productSlug: 'unknown-exam',
			variant: 'unknown-variant',
		};

		// Mock the dbWriter.query for getStandardSummary
		dbWriter.query.mockResolvedValue([]);

		await getGroupedResults({ userExam });

		expect(dbWriter.query).toHaveBeenCalled();
	});
});
