import Sequelize from 'sequelize';

import { dbWriter } from '$daos';

import { getActMathSummary } from '../actExamScoreSummary';
import reduceSummaryResults from '../reduceSummaryResults';

// Mock dependencies
jest.mock('$daos', () => ({
	dbWriter: {
		query: jest.fn(),
	},
}));

jest.mock('../reduceSummaryResults', () => jest.fn());

describe('getActMathSummary', () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	it('should query database and reduce results', async () => {
		const userExam = {
			uuid: 'exam-123',
			productSlug: 'act',
			variant: 'math',
		};

		const mockQueryResults = [
			{ count: 3, status: 'correct', examContentUuid: 'content-1', examContentName: 'Algebra', position: 1 },
			{ count: 2, status: 'incorrect', examContentUuid: 'content-1', examContentName: 'Algebra', position: 1 },
			{ count: 4, status: 'correct', examContentUuid: 'content-2', examContentName: 'Geometry', position: 2 },
			{ count: 1, status: 'incorrect', examContentUuid: 'content-2', examContentName: 'Geometry', position: 2 },
		];

		const mockReducedResults = [
			{ examContentUuid: 'content-1', examContentName: 'Algebra', correct: 3, total: 5, position: 1 },
			{ examContentUuid: 'content-2', examContentName: 'Geometry', correct: 4, total: 5, position: 2 },
		];

		dbWriter.query.mockResolvedValue(mockQueryResults);
		reduceSummaryResults.mockReturnValue(mockReducedResults);

		const result = await getActMathSummary({ userExam });

		expect(dbWriter.query).toHaveBeenCalledWith(expect.any(String), {
			replacements: {
				userExamUuid: 'exam-123',
			},
			raw: true,
			type: Sequelize.QueryTypes.SELECT,
		});

		expect(reduceSummaryResults).toHaveBeenCalledWith({
			results: mockQueryResults,
		});

		expect(result).toEqual(mockReducedResults);
	});

	it('should handle empty query results', async () => {
		const userExam = {
			uuid: 'exam-123',
			productSlug: 'act',
			variant: 'math',
		};

		dbWriter.query.mockResolvedValue([]);
		reduceSummaryResults.mockReturnValue([]);

		const result = await getActMathSummary({ userExam });

		expect(result).toEqual([]);
	});
});
