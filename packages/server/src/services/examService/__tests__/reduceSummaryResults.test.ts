import reduceSummaryResults from '../reduceSummaryResults';

describe('reduceSummaryResults', () => {
  it('should correctly reduce results with single content', () => {
    const results = [
      { examContentUuid: 'content-1', examContentName: 'Content 1', status: 'correct', count: 3, position: 1 },
      { examContentUuid: 'content-1', examContentName: 'Content 1', status: 'incorrect', count: 2, position: 1 }
    ];

    const summary = reduceSummaryResults({ results });
    
    expect(summary).toEqual([
      { examContentUuid: 'content-1', examContentName: 'Content 1', correct: 3, total: 5, position: 1 }
    ]);
  });

  it('should correctly reduce results with multiple contents', () => {
    const results = [
      { examContentUuid: 'content-1', examContentName: 'Content 1', status: 'correct', count: 3, position: 1 },
      { examContentUuid: 'content-1', examContentName: 'Content 1', status: 'incorrect', count: 2, position: 1 },
      { examContentUuid: 'content-2', examContentName: 'Content 2', status: 'correct', count: 1, position: 2 },
      { examContentUuid: 'content-2', examContentName: 'Content 2', status: 'incorrect', count: 4, position: 2 }
    ];

    const summary = reduceSummaryResults({ results });
    
    expect(summary).toEqual([
      { examContentUuid: 'content-1', examContentName: 'Content 1', correct: 3, total: 5, position: 1 },
      { examContentUuid: 'content-2', examContentName: 'Content 2', correct: 1, total: 5, position: 2 }
    ]);
  });

  it('should handle empty results array', () => {
    const results = [];
    const summary = reduceSummaryResults({ results });
    expect(summary).toEqual([]);
  });

  it('should handle results with no correct answers', () => {
    const results = [
      { examContentUuid: 'content-1', examContentName: 'Content 1', status: 'incorrect', count: 5, position: 1 }
    ];

    const summary = reduceSummaryResults({ results });
    
    expect(summary).toEqual([
      { examContentUuid: 'content-1', examContentName: 'Content 1', correct: 0, total: 5, position: 1 }
    ]);
  });

  it('should handle results with all correct answers', () => {
    const results = [
      { examContentUuid: 'content-1', examContentName: 'Content 1', status: 'correct', count: 5, position: 1 }
    ];

    const summary = reduceSummaryResults({ results });
    
    expect(summary).toEqual([
      { examContentUuid: 'content-1', examContentName: 'Content 1', correct: 5, total: 5, position: 1 }
    ]);
  });

  it('should handle results with multiple statuses', () => {
    const results = [
      { examContentUuid: 'content-1', examContentName: 'Content 1', status: 'correct', count: 3, position: 1 },
      { examContentUuid: 'content-1', examContentName: 'Content 1', status: 'incorrect', count: 2, position: 1 },
      { examContentUuid: 'content-1', examContentName: 'Content 1', status: 'skipped', count: 1, position: 1 }
    ];

    const summary = reduceSummaryResults({ results });
    
    expect(summary).toEqual([
      { examContentUuid: 'content-1', examContentName: 'Content 1', correct: 3, total: 6, position: 1 }
    ]);
  });
});