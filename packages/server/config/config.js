const path = require("path");

const { BECOME_ENV } = process.env;
const envPath = BECOME_ENV ? `.env.${BECOME_ENV}` : ".env";

require("dotenv").config({ path: path.resolve(process.cwd(), envPath) });

module.exports = {
	development: {
		use_env_variable: "DATABASE_WRITER_URL",
		seederStorage: "sequelize",
	},
	uat: {
		use_env_variable: "DATABASE_WRITER_URL",
		seederStorage: "sequelize",
	},
	production: {
		use_env_variable: "DATABASE_WRITER_URL",
		seederStorage: "sequelize",
	},
	test: {
		use_env_variable: "DATABASE_WRITER_URL",
		seederStorage: "sequelize",
	},
};
