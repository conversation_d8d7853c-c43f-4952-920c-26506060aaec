module.exports = {
	up: (queryInterface, _Sequelize) => {
		return Promise.all([
			queryInterface.addIndex("productFiles", ["productUuid"]),
			queryInterface.addIndex("productFiles", ["userUuid"]),
		]);
	},

	down: (queryInterface, _Sequelize) => {
		return Promise.all([
			queryInterface.removeIndex("productFiles", ["userUuid"]),
			queryInterface.removeIndex("productFiles", ["productUuid"]),
		]);
	},
};
