const { v4: uuidV4 } = require("uuid");

module.exports = {
	up: (queryInterface, Sequelize) => {
		return Promise.all([
			queryInterface.createTable(
				"feedback",
				{
					uuid: {
						allowNull: false,
						primaryKey: true,
						type: Sequelize.UUID,
						defaultValue: () => uuidV4(),
					},

					actor: Sequelize.STRING,
					verb: Sequelize.STRING,
					object: Sequelize.STRING,
					course: Sequelize.STRING,
					message: Sequelize.TEXT,
					zendeskId: Sequelize.STRING,

					createdAt: Sequelize.DATE,
					updatedAt: Sequelize.DATE,
				},
				{
					charset: "utf8mb4",
					collate: "utf8mb4_unicode_ci",
				}
			),
		]);
	},

	down: (queryInterface, _Sequelize) => {
		return Promise.all([queryInterface.dropTable("feedback")]);
	},
};
