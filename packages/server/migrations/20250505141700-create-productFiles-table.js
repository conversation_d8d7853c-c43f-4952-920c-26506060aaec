const { v4: uuidV4 } = require("uuid");

module.exports = {
	up: (queryInterface, Sequelize) => {
		return Promise.all([
			queryInterface.createTable(
				"productFiles",
				{
					uuid: {
						allowNull: false,
						primaryKey: true,
						type: Sequelize.UUID,
						defaultValue: () => uuidV4(),
					},

					productUuid: {
						allowNull: false,
						type: Sequelize.UUID,
					},
					userUuid: {
						allowNull: false,
						type: Sequelize.UUID,
					},

					name: Sequelize.STRING,
					slug: Sequelize.STRING,
					notes: Sequelize.TEXT(),
					originalFilename: Sequelize.STRING,
					sha256: Sequelize.STRING,
					accessUrl: Sequelize.STRING,

					createdAt: Sequelize.DATE,
					updatedAt: Sequelize.DATE,
				},
				{
					charset: "utf8mb4",
					collate: "utf8mb4_unicode_ci",
				}
			),
		]);
	},

	down: (queryInterface, _Sequelize) => {
		return Promise.all([queryInterface.dropTable("productFiles")]);
	},
};
