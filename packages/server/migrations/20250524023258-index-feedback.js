module.exports = {
	up: (queryInterface, _Sequelize) => {
		return Promise.all([
			queryInterface.addIndex("feedback", ["actor"]),
			queryInterface.addIndex("feedback", ["course"]),
			queryInterface.addIndex("feedback", ["verb"]),
			queryInterface.addIndex("feedback", ["object"]),
			queryInterface.addIndex("feedback", ["zendeskId"]),
		]);
	},
	down: (queryInterface, _Sequelize) => {
		return Promise.all([
			queryInterface.removeIndex("feedback", ["actor"]),
			queryInterface.removeIndex("feedback", ["course"]),
			queryInterface.removeIndex("feedback", ["verb"]),
			queryInterface.removeIndex("feedback", ["object"]),
			queryInterface.removeIndex("feedback", ["zendeskId"]),
		]);
	},
};
