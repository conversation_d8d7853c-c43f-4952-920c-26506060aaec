module.exports = {
	up: (queryInterface, Sequelize) => {
		return Promise.all([
			queryInterface.addColumn(
				'scholarshipApplications',
				'result',
				{
					type: Sequelize.STRING,
					allowNull: true,
					defaultValue: null,
				}
			),
			queryInterface.addColumn(
				'scholarshipApplications',
				'promoCodeUuid',
				{
					type: Sequelize.UUID,
					allowNull: true,
					defaultValue: null,
				}
			),
		]);
	},

	down: (queryInterface, _Sequelize) => {
		return Promise.all([
			queryInterface.removeColumn('scholarshipApplications', 'result'),
			queryInterface.removeColumn('scholarshipApplications', 'promoCodeUuid'),
		]);
	},
};
