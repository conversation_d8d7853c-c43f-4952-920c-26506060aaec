{"name": "dev", "version": "0.0.1-ignore", "scripts": {"gitlab:runner": "docker restart 9d1a9a7d1d52c066a714b33e18ed326241e99bee8a77f5b5fb0e0a9ebc735d97 || docker run --name gitlab-runner --restart always -v /Users/<USER>/gitlab-runner/config:/etc/gitlab-runner -v /var/run/docker.sock:/var/run/docker.sock gitlab/gitlab-runner:latest", "mysql:dump:production": "blackbox_decrypt_file .env && SOURCE=production_reader bash ./scripts/dbDump.sh; rm .env", "mysql:dump:uat": "blackbox_decrypt_file .env && SOURCE=uat bash ./scripts/dbDump.sh; rm .env", "mysql:generateTestDb:local": "blackbox_decrypt_file .env && SOURCE=local bash ./scripts/generateTestDb.sh; rm .env", "mysql:generateTestDb:production": "blackbox_decrypt_file .env && SOURCE=production_reader bash ./scripts/generateTestDb.sh; rm .env", "mysql:generateTestDb:uat": "blackbox_decrypt_file .env && SOURCE=uat bash ./scripts/generateTestDb.sh; rm .env", "mysql:refresh:local": "blackbox_decrypt_file .env && TARGET=local bash ./scripts/dbRefresh.sh; rm .env", "mysql:refresh:localFromEnv": "TARGET=local bash ./scripts/dbRefresh.sh", "mysql:refresh:localFromProdLike": "TARGET=local bash ./scripts/dbProdLikeRefresh.sh", "mysql:refresh:uat": "blackbox_decrypt_file .env && TARGET=uat bash ./scripts/dbRefresh.sh; rm .env", "mysql:setup:local": "bash ./scripts/dbSetup.sh"}, "contributors": ["<PERSON> <<EMAIL>> (https://www.linkedin.com/in/justinpincar)"], "private": true, "dependencies": {"csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "lodash": "^4.17.21", "uuid": "^8.3.2"}}