# Unified IDE Configuration

This document outlines the standardized IDE configuration for the engineering team across this monorepo.

## Overview

The configuration enforces consistent code style, formatting, and linting across all packages using modern tooling and best practices.

## Key Standards

- **Indentation**: Tabs (not spaces)
- **Line Endings**: LF (Unix-style)
- **Quote Style**: Single quotes
- **Semicolons**: Required
- **Print Width**: 90 characters
- **Import Ordering**: Alphabetical with specific groupings

## Configuration Files

### Root Level Configuration

- **`.editorconfig`** - IDE-level settings for consistent formatting
- **`eslintrc.config.mjs`** - Base ESLint configuration with comprehensive rules
- **`.prettierrc.cjs`** - Prettier formatting configuration
- **`.vscode/settings.json`** - VSCode workspace settings
- **`.vscode/extensions.json`** - Recommended VSCode extensions

### Package-Specific Configurations

Each package extends the root configuration with package-specific overrides:

- **`packages/web`** - Next.js app with React, Jest, and Storybook support
- **`packages/server`** - Node.js backend with Jest support
- **`packages/cloudflare`** - Cloudflare Workers environment
- **`packages/tundra-wizard`** - Node.js utility package

## Import Ordering Rules

The ESLint configuration enforces a specific import order:

1. **Built-ins** (lodash, react, react-dom)
2. **External packages** (npm packages)
3. **Internal packages** (achievable-me, apollo)
4. **Parent/sibling imports** (relative imports)
5. **Stylesheets** (.scss files)

Within each group, imports are sorted alphabetically.

## VSCode Setup

### Required Extensions

- **ESLint** (`dbaeumer.vscode-eslint`) - Linting support
- **Prettier** (`esbenp.prettier-vscode`) - Code formatting
- **EditorConfig** (`editorconfig.editorconfig`) - Editor settings

### Recommended Extensions

- **Path Intellisense** (`christian-kohler.path-intellisense`)

### Workspace Settings

The `.vscode/settings.json` file configures:

- Tab-based indentation
- Format on save
- ESLint auto-fix on save
- Import organization
- LF line endings
- Trim trailing whitespace

## Available Scripts

Run these commands from the root directory:

```bash
# Lint all packages
yarn lint

# Lint and auto-fix issues
yarn lint:fix

# Format all files with Prettier
yarn format

# Check formatting without making changes
yarn format:check
```

## Package-Specific Scripts

Each package also supports these scripts locally:

```bash
cd packages/web
yarn lint        # Lint this package only
yarn lint:fix    # Lint and fix this package only
yarn type-check  # Type check this package only
```

## Configuration Inheritance

### ESLint

- Root `eslint.config.mjs` contains base rules and import ordering
- Package configs extend the root and add package-specific rules
- No duplication of common rules

### Prettier

- Single root `.prettierrc.cjs` used by all packages
- Consistent formatting across the entire monorepo

## Troubleshooting

### ESLint Issues

If ESLint is not working properly:

1. Restart VSCode
2. Check that the ESLint extension is enabled
3. Verify the working directories in `.vscode/settings.json`
4. Run `yarn lint` from the command line to see detailed errors
5. Open terminal and check the OUTPUT tab with ESLint selected
6. Check VSCode terminal OUTPUT tab for ESLint server logs

### Prettier Issues

If Prettier is not formatting:

1. Ensure `prettier.requireConfig` is set to `true` in VSCode settings
2. Check that `.prettierrc.cjs` exists in the root
3. Verify `editor.formatOnSave` is enabled
4. Open terminal and check the OUTPUT tab with Prettier selected
5. Check VSCode terminal OUTPUT tab for Prettier server logs

## Best Practices

1. **Always run linting before committing** - Use `yarn lint:fix` to auto-fix issues
2. **Use the recommended VSCode extensions** - They provide the best development experience
3. **Don't override root configurations** unless absolutely necessary
4. **Keep package-specific rules minimal** - Most rules should be in the root config

## Migration Guide

For existing code that doesn't follow these standards:

1. Run `yarn format` to fix formatting issues
2. Run `yarn lint:fix` to auto-fix linting issues
3. Manually fix remaining linting errors
4. Update any custom ESLint/Prettier configs to extend from root
